.class Lcom/topjohnwu/superuser/internal/RootServiceManager$1;
.super Landroid/content/BroadcastReceiver;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/topjohnwu/superuser/internal/RootServiceManager;->createStartRootProcessTask(Landroid/content/Context;Ljava/lang/String;)Ljava/lang/Runnable;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lcom/topjohnwu/superuser/internal/RootServiceManager;

.field final synthetic val$connectArgs:Landroid/os/Bundle;


# direct methods
.method constructor <init>(Lcom/topjohnwu/superuser/internal/RootServiceManager;Landroid/os/Bundle;)V
    .locals 0

    iput-object p1, p0, Lcom/topjohnwu/superuser/internal/RootServiceManager$1;->this$0:Lcom/topjohnwu/superuser/internal/RootServiceManager;

    iput-object p2, p0, Lcom/topjohnwu/superuser/internal/RootServiceManager$1;->val$connectArgs:Landroid/os/Bundle;

    invoke-direct {p0}, Landroid/content/BroadcastReceiver;-><init>()V

    return-void
.end method


# virtual methods
.method public onReceive(Landroid/content/Context;Landroid/content/Intent;)V
    .locals 2

    iget-object p1, p0, Lcom/topjohnwu/superuser/internal/RootServiceManager$1;->this$0:Lcom/topjohnwu/superuser/internal/RootServiceManager;

    invoke-virtual {p1}, Lcom/topjohnwu/superuser/internal/RootServiceManager;->binderDied()V

    const-string p1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->UyCrhˑᵎʾᐧˈᵎיᵔˈﹳˉᐧᴵـʿʿBqnAN()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Landroid/content/Intent;->getBundleExtra(Ljava/lang/String;)Landroid/os/Bundle;

    move-result-object p1

    if-nez p1, :cond_0

    return-void

    :cond_0
    const-string p2, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->mgNZTʼˏᐧﾞﹳᵔˉʿʻⁱʻˆfLJLs()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2}, Landroid/os/Bundle;->getBinder(Ljava/lang/String;)Landroid/os/IBinder;

    move-result-object p1

    if-nez p1, :cond_1

    return-void

    :cond_1
    invoke-static {p1}, Lcom/topjohnwu/superuser/internal/IRootServiceManager$Stub;->asInterface(Landroid/os/IBinder;)Lcom/topjohnwu/superuser/internal/IRootServiceManager;

    move-result-object p2

    :try_start_0
    iget-object v0, p0, Lcom/topjohnwu/superuser/internal/RootServiceManager$1;->this$0:Lcom/topjohnwu/superuser/internal/RootServiceManager;

    const/4 v1, 0x0

    invoke-interface {p1, v0, v1}, Landroid/os/IBinder;->linkToDeath(Landroid/os/IBinder$DeathRecipient;I)V

    iget-object v0, p0, Lcom/topjohnwu/superuser/internal/RootServiceManager$1;->val$connectArgs:Landroid/os/Bundle;

    invoke-interface {p2, v0}, Lcom/topjohnwu/superuser/internal/IRootServiceManager;->connect(Landroid/os/Bundle;)V

    iget-object p2, p0, Lcom/topjohnwu/superuser/internal/RootServiceManager$1;->this$0:Lcom/topjohnwu/superuser/internal/RootServiceManager;

    invoke-static {p2, p1}, Lcom/topjohnwu/superuser/internal/RootServiceManager;->access$002(Lcom/topjohnwu/superuser/internal/RootServiceManager;Landroid/os/IBinder;)Landroid/os/IBinder;
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    const-string p2, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->vHWxZˈʾـﹶˆﾞᵔʿˆʼʽʽRMvao()Ljava/lang/String;

    move-result-object p2

    invoke-static {p2, p1}, Lcom/topjohnwu/superuser/internal/Utils;->err(Ljava/lang/String;Ljava/lang/Throwable;)V

    :goto_0
    return-void
.end method
