.class public final synthetic Lcom/api/-$$Lambda$KeyAuth$i676pfRaXgOjM6ss1fBQqs15eqA;
.super Ljava/lang/Object;
.source "lambda"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic f$0:Lcom/api/KeyAuth;

.field public final synthetic f$1:Ljava/lang/String;

.field public final synthetic f$2:Lcom/api/KeyAuth$LicenseCallback;


# direct methods
.method public synthetic constructor <init>(Lcom/api/KeyAuth;Ljava/lang/String;Lcom/api/KeyAuth$LicenseCallback;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/api/-$$Lambda$KeyAuth$i676pfRaXgOjM6ss1fBQqs15eqA;->f$0:Lcom/api/KeyAuth;

    iput-object p2, p0, Lcom/api/-$$Lambda$KeyAuth$i676pfRaXgOjM6ss1fBQqs15eqA;->f$1:Ljava/lang/String;

    iput-object p3, p0, Lcom/api/-$$Lambda$KeyAuth$i676pfRaXgOjM6ss1fBQqs15eqA;->f$2:Lcom/api/KeyAuth$LicenseCallback;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 3

    iget-object v0, p0, Lcom/api/-$$Lambda$KeyAuth$i676pfRaXgOjM6ss1fBQqs15eqA;->f$0:Lcom/api/KeyAuth;

    iget-object v1, p0, Lcom/api/-$$Lambda$KeyAuth$i676pfRaXgOjM6ss1fBQqs15eqA;->f$1:Ljava/lang/String;

    iget-object v2, p0, Lcom/api/-$$Lambda$KeyAuth$i676pfRaXgOjM6ss1fBQqs15eqA;->f$2:Lcom/api/KeyAuth$LicenseCallback;

    invoke-virtual {v0, v1, v2}, Lcom/api/KeyAuth;->lambda$license$3$KeyAuth(Ljava/lang/String;Lcom/api/KeyAuth$LicenseCallback;)V

    return-void
.end method
