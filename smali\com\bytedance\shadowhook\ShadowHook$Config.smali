.class public Lcom/bytedance/shadowhook/ShadowHook$Config;
.super Ljava/lang/Object;
.source "ShadowHook.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/shadowhook/ShadowHook;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Config"
.end annotation


# instance fields
.field private debuggable:Z

.field private libLoader:Lcom/bytedance/shadowhook/ShadowHook$ILibLoader;

.field private mode:I

.field private recordable:Z


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 303
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 304
    return-void
.end method


# virtual methods
.method public getDebuggable()Z
    .locals 1

    .line 327
    iget-boolean v0, p0, Lcom/bytedance/shadowhook/ShadowHook$Config;->debuggable:Z

    return v0
.end method

.method public getLibLoader()Lcom/bytedance/shadowhook/ShadowHook$ILibLoader;
    .locals 1

    .line 311
    iget-object v0, p0, Lcom/bytedance/shadowhook/ShadowHook$Config;->libLoader:Lcom/bytedance/shadowhook/ShadowHook$ILibLoader;

    return-object v0
.end method

.method public getMode()I
    .locals 1

    .line 319
    iget v0, p0, Lcom/bytedance/shadowhook/ShadowHook$Config;->mode:I

    return v0
.end method

.method public getRecordable()Z
    .locals 1

    .line 335
    iget-boolean v0, p0, Lcom/bytedance/shadowhook/ShadowHook$Config;->recordable:Z

    return v0
.end method

.method public setDebuggable(Z)V
    .locals 0
    .param p1, "debuggable"    # Z

    .line 323
    iput-boolean p1, p0, Lcom/bytedance/shadowhook/ShadowHook$Config;->debuggable:Z

    .line 324
    return-void
.end method

.method public setLibLoader(Lcom/bytedance/shadowhook/ShadowHook$ILibLoader;)V
    .locals 0
    .param p1, "libLoader"    # Lcom/bytedance/shadowhook/ShadowHook$ILibLoader;

    .line 307
    iput-object p1, p0, Lcom/bytedance/shadowhook/ShadowHook$Config;->libLoader:Lcom/bytedance/shadowhook/ShadowHook$ILibLoader;

    .line 308
    return-void
.end method

.method public setMode(I)V
    .locals 0
    .param p1, "mode"    # I

    .line 315
    iput p1, p0, Lcom/bytedance/shadowhook/ShadowHook$Config;->mode:I

    .line 316
    return-void
.end method

.method public setRecordable(Z)V
    .locals 0
    .param p1, "recordable"    # Z

    .line 331
    iput-boolean p1, p0, Lcom/bytedance/shadowhook/ShadowHook$Config;->recordable:Z

    .line 332
    return-void
.end method
