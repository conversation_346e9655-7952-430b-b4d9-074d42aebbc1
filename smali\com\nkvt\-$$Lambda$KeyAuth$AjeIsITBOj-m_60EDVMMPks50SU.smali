.class public final synthetic Lcom/nkvt/-$$Lambda$KeyAuth$AjeIsITBOj-m_60EDVMMPks50SU;
.super Ljava/lang/Object;
.source "lambda"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic f$0:Lcom/nkvt/KeyAuth;

.field public final synthetic f$1:Lcom/nkvt/KeyAuth$InitCallback;


# direct methods
.method public synthetic constructor <init>(Lcom/nkvt/KeyAuth;Lcom/nkvt/KeyAuth$InitCallback;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/nkvt/-$$Lambda$KeyAuth$AjeIsITBOj-m_60EDVMMPks50SU;->f$0:Lcom/nkvt/KeyAuth;

    iput-object p2, p0, Lcom/nkvt/-$$Lambda$KeyAuth$AjeIsITBOj-m_60EDVMMPks50SU;->f$1:Lcom/nkvt/KeyAuth$InitCallback;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    iget-object v0, p0, Lcom/nkvt/-$$Lambda$KeyAuth$AjeIsITBOj-m_60EDVMMPks50SU;->f$0:Lcom/nkvt/KeyAuth;

    iget-object v1, p0, Lcom/nkvt/-$$Lambda$KeyAuth$AjeIsITBOj-m_60EDVMMPks50SU;->f$1:Lcom/nkvt/KeyAuth$InitCallback;

    invoke-virtual {v0, v1}, Lcom/nkvt/KeyAuth;->lambda$init$0$KeyAuth(Lcom/nkvt/KeyAuth$InitCallback;)V

    return-void
.end method
