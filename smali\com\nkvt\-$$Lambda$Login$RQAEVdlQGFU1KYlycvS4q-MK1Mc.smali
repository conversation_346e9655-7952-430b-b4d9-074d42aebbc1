.class public final synthetic Lcom/nkvt/-$$Lambda$Login$RQAEVdlQGFU1KYlycvS4q-MK1Mc;
.super Ljava/lang/Object;
.source "lambda"

# interfaces
.implements Landroid/widget/CompoundButton$OnCheckedChangeListener;


# instance fields
.field public final synthetic f$0:Lcom/nkvt/Login;

.field public final synthetic f$1:Landroid/widget/LinearLayout;

.field public final synthetic f$2:Landroid/widget/EditText;

.field public final synthetic f$3:Landroid/widget/TextView;

.field public final synthetic f$4:Landroid/widget/TextView;


# direct methods
.method public synthetic constructor <init>(Lcom/nkvt/Login;Landroid/widget/LinearLayout;Landroid/widget/EditText;Landroid/widget/TextView;Landroid/widget/TextView;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/nkvt/-$$Lambda$Login$RQAEVdlQGFU1KYlycvS4q-MK1Mc;->f$0:Lcom/nkvt/Login;

    iput-object p2, p0, Lcom/nkvt/-$$Lambda$Login$RQAEVdlQGFU1KYlycvS4q-MK1Mc;->f$1:Landroid/widget/LinearLayout;

    iput-object p3, p0, Lcom/nkvt/-$$Lambda$Login$RQAEVdlQGFU1KYlycvS4q-MK1Mc;->f$2:Landroid/widget/EditText;

    iput-object p4, p0, Lcom/nkvt/-$$Lambda$Login$RQAEVdlQGFU1KYlycvS4q-MK1Mc;->f$3:Landroid/widget/TextView;

    iput-object p5, p0, Lcom/nkvt/-$$Lambda$Login$RQAEVdlQGFU1KYlycvS4q-MK1Mc;->f$4:Landroid/widget/TextView;

    return-void
.end method


# virtual methods
.method public final onCheckedChanged(Landroid/widget/CompoundButton;Z)V
    .locals 7

    iget-object v0, p0, Lcom/nkvt/-$$Lambda$Login$RQAEVdlQGFU1KYlycvS4q-MK1Mc;->f$0:Lcom/nkvt/Login;

    iget-object v1, p0, Lcom/nkvt/-$$Lambda$Login$RQAEVdlQGFU1KYlycvS4q-MK1Mc;->f$1:Landroid/widget/LinearLayout;

    iget-object v2, p0, Lcom/nkvt/-$$Lambda$Login$RQAEVdlQGFU1KYlycvS4q-MK1Mc;->f$2:Landroid/widget/EditText;

    iget-object v3, p0, Lcom/nkvt/-$$Lambda$Login$RQAEVdlQGFU1KYlycvS4q-MK1Mc;->f$3:Landroid/widget/TextView;

    iget-object v4, p0, Lcom/nkvt/-$$Lambda$Login$RQAEVdlQGFU1KYlycvS4q-MK1Mc;->f$4:Landroid/widget/TextView;

    move-object v5, p1

    move v6, p2

    invoke-virtual/range {v0 .. v6}, Lcom/nkvt/Login;->lambda$Init$0$Login(Landroid/widget/LinearLayout;Landroid/widget/EditText;Landroid/widget/TextView;Landroid/widget/TextView;Landroid/widget/CompoundButton;Z)V

    return-void
.end method
