.class public final synthetic Lcom/nkvt/-$$Lambda$Login$2RwmG7oymzyLM9eRoTbzwYJCzjE;
.super Ljava/lang/Object;
.source "lambda"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic f$0:Lcom/nkvt/Login;


# direct methods
.method public synthetic constructor <init>(Lcom/nkvt/Login;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/nkvt/-$$Lambda$Login$2RwmG7oymzyLM9eRoTbzwYJCzjE;->f$0:Lcom/nkvt/Login;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 1

    iget-object v0, p0, Lcom/nkvt/-$$Lambda$Login$2RwmG7oymzyLM9eRoTbzwYJCzjE;->f$0:Lcom/nkvt/Login;

    invoke-virtual {v0}, Lcom/nkvt/Login;->lambda$null$1$Login()V

    return-void
.end method
