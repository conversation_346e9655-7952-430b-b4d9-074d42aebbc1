.class public Lcom/api/KeyAuth;
.super Ljava/lang/Object;
.source "KeyAuth.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/api/KeyAuth$WebhookCallback;,
        Lcom/api/KeyAuth$BanCallback;,
        Lcom/api/KeyAuth$LicenseCallback;,
        Lcom/api/KeyAuth$UpgradeCallback;,
        Lcom/api/KeyAuth$LoginCallback;,
        Lcom/api/KeyAuth$InitCallback;
    }
.end annotation


# instance fields
.field private final appname:Ljava/lang/String;

.field private initialized:Z

.field private final ownerid:Ljava/lang/String;

.field private sessionid:Ljava/lang/String;

.field private final url:Ljava/lang/String;

.field private userData:Lcom/api/UserData;

.field private final version:Ljava/lang/String;


# direct methods
.method public constructor <init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 0
    .param p1, "appname"    # Ljava/lang/String;
    .param p2, "ownerid"    # Ljava/lang/String;
    .param p3, "version"    # Ljava/lang/String;
    .param p4, "url"    # Ljava/lang/String;

    .line 24
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 25
    iput-object p1, p0, Lcom/api/KeyAuth;->appname:Ljava/lang/String;

    .line 26
    iput-object p2, p0, Lcom/api/KeyAuth;->ownerid:Ljava/lang/String;

    .line 27
    iput-object p3, p0, Lcom/api/KeyAuth;->version:Ljava/lang/String;

    .line 28
    iput-object p4, p0, Lcom/api/KeyAuth;->url:Ljava/lang/String;

    .line 29
    return-void
.end method

.method private varargs sendPostRequest(Ljava/lang/String;[Ljava/lang/String;)Ljava/lang/String;
    .locals 8
    .param p1, "endpoint"    # Ljava/lang/String;
    .param p2, "params"    # [Ljava/lang/String;

    .line 37
    :try_start_0
    new-instance v0, Ljava/net/URL;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v2, p0, Lcom/api/KeyAuth;->url:Ljava/lang/String;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/net/URL;-><init>(Ljava/lang/String;)V

    .line 38
    .local v0, "url":Ljava/net/URL;
    invoke-virtual {v0}, Ljava/net/URL;->openConnection()Ljava/net/URLConnection;

    move-result-object v1

    check-cast v1, Ljava/net/HttpURLConnection;

    .line 39
    .local v1, "conn":Ljava/net/HttpURLConnection;
    const-string v2, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->FCeuBﾞᵔـʿˈʿˈﹶﹳᐧיˋᵢʿٴˑvaVTJ()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/net/HttpURLConnection;->setRequestMethod(Ljava/lang/String;)V

    .line 40
    const-string v2, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->YqTLJᐧˊʾˋˉˊᐧﹶʻᵎˎᵔʼˊʽˏFDQZM()Ljava/lang/String;

    move-result-object v2

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->dmOcTʿʼʾᴵˈʿˏᵎᵢˋᵢˋImNBx()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v2, v3}, Ljava/net/HttpURLConnection;->setRequestProperty(Ljava/lang/String;Ljava/lang/String;)V

    .line 41
    const/4 v2, 0x1

    invoke-virtual {v1, v2}, Ljava/net/HttpURLConnection;->setDoOutput(Z)V

    .line 43
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 44
    .local v2, "postData":Ljava/lang/StringBuilder;
    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->skIPPᐧʽᵔᵎˏᐧـʻᐧʿfgzqT()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 45
    const/4 v3, 0x0

    .local v3, "i":I
    :goto_0
    array-length v4, p2

    if-ge v3, v4, :cond_0

    .line 46
    const/16 v4, 0x26

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    aget-object v4, p2, v3

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/16 v4, 0x3d

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    add-int/lit8 v4, v3, 0x1

    aget-object v4, p2, v4

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 45
    add-int/lit8 v3, v3, 0x2

    goto :goto_0

    .line 49
    .end local v3    # "i":I
    :cond_0
    invoke-virtual {v1}, Ljava/net/HttpURLConnection;->getOutputStream()Ljava/io/OutputStream;

    move-result-object v3

    .line 50
    .local v3, "os":Ljava/io/OutputStream;
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    const-string v5, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->OQUGbᵢˉٴˆˈיˉـˋʼיˎᴵـicimh()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v4, v5}, Ljava/lang/String;->getBytes(Ljava/lang/String;)[B

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/io/OutputStream;->write([B)V

    .line 51
    invoke-virtual {v3}, Ljava/io/OutputStream;->flush()V

    .line 52
    invoke-virtual {v3}, Ljava/io/OutputStream;->close()V

    .line 54
    new-instance v4, Ljava/io/BufferedReader;

    new-instance v5, Ljava/io/InputStreamReader;

    invoke-virtual {v1}, Ljava/net/HttpURLConnection;->getInputStream()Ljava/io/InputStream;

    move-result-object v6

    invoke-direct {v5, v6}, Ljava/io/InputStreamReader;-><init>(Ljava/io/InputStream;)V

    invoke-direct {v4, v5}, Ljava/io/BufferedReader;-><init>(Ljava/io/Reader;)V

    .line 55
    .local v4, "in":Ljava/io/BufferedReader;
    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    .line 57
    .local v5, "response":Ljava/lang/StringBuilder;
    :goto_1
    invoke-virtual {v4}, Ljava/io/BufferedReader;->readLine()Ljava/lang/String;

    move-result-object v6

    move-object v7, v6

    .local v7, "line":Ljava/lang/String;
    if-eqz v6, :cond_1

    .line 58
    invoke-virtual {v5, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_1

    .line 60
    :cond_1
    invoke-virtual {v4}, Ljava/io/BufferedReader;->close()V

    .line 62
    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v6
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    return-object v6

    .line 63
    .end local v0    # "url":Ljava/net/URL;
    .end local v1    # "conn":Ljava/net/HttpURLConnection;
    .end local v2    # "postData":Ljava/lang/StringBuilder;
    .end local v3    # "os":Ljava/io/OutputStream;
    .end local v4    # "in":Ljava/io/BufferedReader;
    .end local v5    # "response":Ljava/lang/StringBuilder;
    .end local v7    # "line":Ljava/lang/String;
    :catch_0
    move-exception v0

    .line 64
    .local v0, "e":Ljava/lang/Exception;
    invoke-virtual {v0}, Ljava/lang/Exception;->printStackTrace()V

    .line 65
    const/4 v1, 0x0

    return-object v1
.end method


# virtual methods
.method public ban(Lcom/api/KeyAuth$BanCallback;)V
    .locals 2
    .param p1, "callback"    # Lcom/api/KeyAuth$BanCallback;

    .line 275
    iget-boolean v0, p0, Lcom/api/KeyAuth;->initialized:Z

    if-nez v0, :cond_0

    .line 276
    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->VPGLvﹶˑʻˎʿˏᐧˏˏˋⁱDrMkh()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 277
    return-void

    .line 280
    :cond_0
    new-instance v0, Ljava/lang/Thread;

    new-instance v1, Lcom/api/-$$Lambda$KeyAuth$e1l2clvtOP-ayd03aVUoJlK_V_8;

    invoke-direct {v1, p0, p1}, Lcom/api/-$$Lambda$KeyAuth$e1l2clvtOP-ayd03aVUoJlK_V_8;-><init>(Lcom/api/KeyAuth;Lcom/api/KeyAuth$BanCallback;)V

    invoke-direct {v0, v1}, Ljava/lang/Thread;-><init>(Ljava/lang/Runnable;)V

    .line 313
    invoke-virtual {v0}, Ljava/lang/Thread;->start()V

    .line 314
    return-void
.end method

.method public getUserData()Lcom/api/UserData;
    .locals 1

    .line 32
    iget-object v0, p0, Lcom/api/KeyAuth;->userData:Lcom/api/UserData;

    return-object v0
.end method

.method public init(Lcom/api/KeyAuth$InitCallback;)V
    .locals 2
    .param p1, "callback"    # Lcom/api/KeyAuth$InitCallback;

    .line 102
    new-instance v0, Ljava/lang/Thread;

    new-instance v1, Lcom/api/-$$Lambda$KeyAuth$k-TlLm1bF3VZLVVAeQKUNq6Rwns;

    invoke-direct {v1, p0, p1}, Lcom/api/-$$Lambda$KeyAuth$k-TlLm1bF3VZLVVAeQKUNq6Rwns;-><init>(Lcom/api/KeyAuth;Lcom/api/KeyAuth$InitCallback;)V

    invoke-direct {v0, v1}, Ljava/lang/Thread;-><init>(Ljava/lang/Runnable;)V

    .line 135
    invoke-virtual {v0}, Ljava/lang/Thread;->start()V

    .line 136
    return-void
.end method

.method public synthetic lambda$ban$4$KeyAuth(Lcom/api/KeyAuth$BanCallback;)V
    .locals 5
    .param p1, "callback"    # Lcom/api/KeyAuth$BanCallback;

    .line 281
    invoke-static {}, Lcom/api/HWID;->getHWID()Ljava/lang/String;

    move-result-object v0

    .line 283
    .local v0, "hwid":Ljava/lang/String;
    const/4 v1, 0x6

    new-array v1, v1, [Ljava/lang/String;

    const/4 v2, 0x0

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->fCfrvʾⁱʿיˊᵔᵎיﹳˆٴˆˉⁱﹶˎRTRMc()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v1, v2

    iget-object v2, p0, Lcom/api/KeyAuth;->sessionid:Ljava/lang/String;

    const/4 v3, 0x1

    aput-object v2, v1, v3

    const/4 v2, 0x2

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->oUbJsـⁱــʾᐧᴵˆʻʻʽﹳHTzVU()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v1, v2

    iget-object v2, p0, Lcom/api/KeyAuth;->appname:Ljava/lang/String;

    const/4 v3, 0x3

    aput-object v2, v1, v3

    const/4 v2, 0x4

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->nVYWHˋⁱˆˉʻﹶיﹶʾˎˈˑﾞʻˑﹳᐧˎʻʻbIGJb()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v1, v2

    iget-object v2, p0, Lcom/api/KeyAuth;->ownerid:Ljava/lang/String;

    const/4 v3, 0x5

    aput-object v2, v1, v3

    const-string v2, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->OLHkrᵔיᵎיˏיʽˋﹶⁱsoEMc()Ljava/lang/String;

    move-result-object v2

    invoke-direct {p0, v2, v1}, Lcom/api/KeyAuth;->sendPostRequest(Ljava/lang/String;[Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    .line 289
    .local v1, "response":Ljava/lang/String;
    if-eqz v1, :cond_2

    .line 291
    :try_start_0
    new-instance v2, Lorg/json/JSONObject;

    invoke-direct {v2, v1}, Lorg/json/JSONObject;-><init>(Ljava/lang/String;)V

    .line 293
    .local v2, "responseJSON":Lorg/json/JSONObject;
    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->DohHWᵢﾞˎˏˏⁱˉʽʿייᵎʾˎᴵˊᐧـkgONy()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Lorg/json/JSONObject;->getBoolean(Ljava/lang/String;)Z

    move-result v3

    if-eqz v3, :cond_0

    .line 294
    if-eqz p1, :cond_1

    .line 295
    invoke-interface {p1}, Lcom/api/KeyAuth$BanCallback;->onSuccess()V

    goto :goto_0

    .line 298
    :cond_0
    if-eqz p1, :cond_1

    .line 299
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->UpjlLﾞʻˎʿʽʾᵎᵎˏⁱﹳⁱﹶʾˎˉosaBd()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v4, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->TDCfoʼˊˋˎⁱˋʿـⁱˋHdgkG()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v4}, Lorg/json/JSONObject;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-interface {p1, v3}, Lcom/api/KeyAuth$BanCallback;->onFailure(Ljava/lang/String;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    .line 302
    .end local v2    # "responseJSON":Lorg/json/JSONObject;
    :catch_0
    move-exception v2

    .line 303
    .local v2, "e":Ljava/lang/Exception;
    invoke-virtual {v2}, Ljava/lang/Exception;->printStackTrace()V

    .line 304
    if-eqz p1, :cond_1

    .line 305
    invoke-virtual {v2}, Ljava/lang/Exception;->getMessage()Ljava/lang/String;

    move-result-object v3

    invoke-interface {p1, v3}, Lcom/api/KeyAuth$BanCallback;->onFailure(Ljava/lang/String;)V

    .line 307
    .end local v2    # "e":Ljava/lang/Exception;
    :cond_1
    :goto_0
    goto :goto_1

    .line 309
    :cond_2
    if-eqz p1, :cond_3

    .line 310
    const-string v2, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->YefNBˋᴵᴵﹶᵔˋˆˊuklGR()Ljava/lang/String;

    move-result-object v2

    invoke-interface {p1, v2}, Lcom/api/KeyAuth$BanCallback;->onFailure(Ljava/lang/String;)V

    .line 313
    :cond_3
    :goto_1
    return-void
.end method

.method public synthetic lambda$init$0$KeyAuth(Lcom/api/KeyAuth$InitCallback;)V
    .locals 4
    .param p1, "callback"    # Lcom/api/KeyAuth$InitCallback;

    .line 103
    const/4 v0, 0x6

    new-array v0, v0, [Ljava/lang/String;

    const/4 v1, 0x0

    const-string v2, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->JXWKzˆـⁱʿﾞˈﹶﹶˎﹶgSfgg()Ljava/lang/String;

    move-result-object v2

    aput-object v2, v0, v1

    iget-object v1, p0, Lcom/api/KeyAuth;->version:Ljava/lang/String;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    const/4 v1, 0x2

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->VPNYUᵢˉᵢﾞˑᵔﹶʻـٴיـﹶʿˆـdBvuL()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v0, v1

    iget-object v1, p0, Lcom/api/KeyAuth;->appname:Ljava/lang/String;

    const/4 v3, 0x3

    aput-object v1, v0, v3

    const/4 v1, 0x4

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->bWlSFᵔʽʾﹳﹶʾﹳﾞˆˆٴʾⁱˎxlXRD()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v0, v1

    iget-object v1, p0, Lcom/api/KeyAuth;->ownerid:Ljava/lang/String;

    const/4 v3, 0x5

    aput-object v1, v0, v3

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->HplAgיˎˋˈʾⁱיﹳٴˎﹶٴˉᴵʿʾʻˈﹶʾmVEiF()Ljava/lang/String;

    move-result-object v1

    invoke-direct {p0, v1, v0}, Lcom/api/KeyAuth;->sendPostRequest(Ljava/lang/String;[Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 109
    .local v0, "response":Ljava/lang/String;
    if-eqz v0, :cond_2

    .line 111
    :try_start_0
    new-instance v1, Lorg/json/JSONObject;

    invoke-direct {v1, v0}, Lorg/json/JSONObject;-><init>(Ljava/lang/String;)V

    .line 113
    .local v1, "responseJSON":Lorg/json/JSONObject;
    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->vEBMnﾞᵔˆᵢٴⁱʼˉˆʽˎˏʾˊיiOiFF()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v3}, Lorg/json/JSONObject;->getBoolean(Ljava/lang/String;)Z

    move-result v3

    if-eqz v3, :cond_0

    .line 114
    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->NlwCvﹶˎⁱʼˋʽʼᐧʼﹶⁱـﹶDvBud()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v3}, Lorg/json/JSONObject;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    iput-object v3, p0, Lcom/api/KeyAuth;->sessionid:Ljava/lang/String;

    .line 115
    iput-boolean v2, p0, Lcom/api/KeyAuth;->initialized:Z

    .line 116
    if-eqz p1, :cond_1

    .line 117
    invoke-interface {p1}, Lcom/api/KeyAuth$InitCallback;->onSuccess()V

    goto :goto_0

    .line 120
    :cond_0
    if-eqz p1, :cond_1

    .line 121
    const-string v2, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->ivovRٴˈˑᐧʼﹶˑˈᐧʻٴﹶﹶˎᵎtlQAb()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Lorg/json/JSONObject;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-interface {p1, v2}, Lcom/api/KeyAuth$InitCallback;->onFailure(Ljava/lang/String;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    .line 124
    .end local v1    # "responseJSON":Lorg/json/JSONObject;
    :catch_0
    move-exception v1

    .line 125
    .local v1, "e":Ljava/lang/Exception;
    invoke-virtual {v1}, Ljava/lang/Exception;->printStackTrace()V

    .line 126
    if-eqz p1, :cond_1

    .line 127
    invoke-virtual {v1}, Ljava/lang/Exception;->getMessage()Ljava/lang/String;

    move-result-object v2

    invoke-interface {p1, v2}, Lcom/api/KeyAuth$InitCallback;->onFailure(Ljava/lang/String;)V

    .line 129
    .end local v1    # "e":Ljava/lang/Exception;
    :cond_1
    :goto_0
    goto :goto_1

    .line 131
    :cond_2
    if-eqz p1, :cond_3

    .line 132
    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->dmPEuᵢˑʾʻˊᵢˋˈʾˉיˊˊʼﹶᵢIubZf()Ljava/lang/String;

    move-result-object v1

    invoke-interface {p1, v1}, Lcom/api/KeyAuth$InitCallback;->onFailure(Ljava/lang/String;)V

    .line 135
    :cond_3
    :goto_1
    return-void
.end method

.method public synthetic lambda$license$3$KeyAuth(Ljava/lang/String;Lcom/api/KeyAuth$LicenseCallback;)V
    .locals 4
    .param p1, "key"    # Ljava/lang/String;
    .param p2, "callback"    # Lcom/api/KeyAuth$LicenseCallback;

    .line 236
    invoke-static {}, Lcom/api/HWID;->getHWID()Ljava/lang/String;

    move-result-object v0

    .line 238
    .local v0, "hwid":Ljava/lang/String;
    const/16 v1, 0xa

    new-array v1, v1, [Ljava/lang/String;

    const/4 v2, 0x0

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->jyASZʿˈˉᵢﾞˆˏʾיᐧﹶᵔˆיˏٴᴵʾXUCiX()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v1, v2

    const/4 v2, 0x1

    aput-object p1, v1, v2

    const/4 v2, 0x2

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->DHGqhˏـٴᐧᵔٴˈˈᐧˈʿˈEQASm()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v1, v2

    const/4 v2, 0x3

    aput-object v0, v1, v2

    const/4 v2, 0x4

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->LVDDaˎʽˆʿᵔˎﹶـʾˉˈʿﾞʼـˊwYZED()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v1, v2

    iget-object v2, p0, Lcom/api/KeyAuth;->sessionid:Ljava/lang/String;

    const/4 v3, 0x5

    aput-object v2, v1, v3

    const/4 v2, 0x6

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->yHbGTˋˈـᴵʾʾיʾﹳˋˎRSFyQ()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v1, v2

    iget-object v2, p0, Lcom/api/KeyAuth;->appname:Ljava/lang/String;

    const/4 v3, 0x7

    aput-object v2, v1, v3

    const/16 v2, 0x8

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->EiGysـʻᵔٴˉﹶـʾﹳˏـˎʽﹶᐧᵔZbugM()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v1, v2

    iget-object v2, p0, Lcom/api/KeyAuth;->ownerid:Ljava/lang/String;

    const/16 v3, 0x9

    aput-object v2, v1, v3

    const-string v2, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->IsTlZיʾᵔᵔʾᵢʼʼᵎⁱﾞˎkUeFK()Ljava/lang/String;

    move-result-object v2

    invoke-direct {p0, v2, v1}, Lcom/api/KeyAuth;->sendPostRequest(Ljava/lang/String;[Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    .line 246
    .local v1, "response":Ljava/lang/String;
    if-eqz v1, :cond_2

    .line 248
    :try_start_0
    new-instance v2, Lorg/json/JSONObject;

    invoke-direct {v2, v1}, Lorg/json/JSONObject;-><init>(Ljava/lang/String;)V

    .line 250
    .local v2, "responseJSON":Lorg/json/JSONObject;
    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->XNICDʽـˏᴵˑʽʽᵢᵢᵎˈʾᵔᵔٴʾˎʻrZcnj()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Lorg/json/JSONObject;->getBoolean(Ljava/lang/String;)Z

    move-result v3

    if-eqz v3, :cond_0

    .line 251
    new-instance v3, Lcom/api/UserData;

    invoke-direct {v3, v2}, Lcom/api/UserData;-><init>(Lorg/json/JSONObject;)V

    iput-object v3, p0, Lcom/api/KeyAuth;->userData:Lcom/api/UserData;

    .line 252
    if-eqz p2, :cond_1

    .line 253
    invoke-interface {p2, v3}, Lcom/api/KeyAuth$LicenseCallback;->onSuccess(Lcom/api/UserData;)V

    goto :goto_0

    .line 256
    :cond_0
    if-eqz p2, :cond_1

    .line 257
    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->QZbykיᵢٴʿʾʾᵢⁱˋᴵˊᵢʾᵢˑˏʿᐧᴵᴵﹳʼᐧˊcWnST()Ljava/lang/String;

    move-result-object v3

    invoke-interface {p2, v3}, Lcom/api/KeyAuth$LicenseCallback;->onFailure(Ljava/lang/String;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    .line 260
    .end local v2    # "responseJSON":Lorg/json/JSONObject;
    :catch_0
    move-exception v2

    .line 261
    .local v2, "e":Ljava/lang/Exception;
    invoke-virtual {v2}, Ljava/lang/Exception;->printStackTrace()V

    .line 262
    if-eqz p2, :cond_1

    .line 263
    invoke-virtual {v2}, Ljava/lang/Exception;->getMessage()Ljava/lang/String;

    move-result-object v3

    invoke-interface {p2, v3}, Lcom/api/KeyAuth$LicenseCallback;->onFailure(Ljava/lang/String;)V

    .line 265
    .end local v2    # "e":Ljava/lang/Exception;
    :cond_1
    :goto_0
    goto :goto_1

    .line 267
    :cond_2
    if-eqz p2, :cond_3

    .line 268
    const-string v2, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->IaNWEˈᵎʾﾞﹳʻʿˊﹳـﹳᵎˈˈˊʿʽᵢﾞCHVLU()Ljava/lang/String;

    move-result-object v2

    invoke-interface {p2, v2}, Lcom/api/KeyAuth$LicenseCallback;->onFailure(Ljava/lang/String;)V

    .line 271
    :cond_3
    :goto_1
    return-void
.end method

.method public synthetic lambda$login$1$KeyAuth(Ljava/lang/String;Ljava/lang/String;Lcom/api/KeyAuth$LoginCallback;)V
    .locals 5
    .param p1, "username"    # Ljava/lang/String;
    .param p2, "password"    # Ljava/lang/String;
    .param p3, "callback"    # Lcom/api/KeyAuth$LoginCallback;

    .line 145
    invoke-static {}, Lcom/api/HWID;->getHWID()Ljava/lang/String;

    move-result-object v0

    .line 147
    .local v0, "hwid":Ljava/lang/String;
    const/16 v1, 0xc

    new-array v1, v1, [Ljava/lang/String;

    const/4 v2, 0x0

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->TocBjʽﹶˊˈˑـʿᵎgAyZV()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v1, v2

    const/4 v2, 0x1

    aput-object p1, v1, v2

    const/4 v2, 0x2

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->sOJexˋʻﾞᴵᵎᐧﹳᵢʾﾞˏˏᵔﾞʽᵎˎˎﾞᵎpPVzI()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v1, v2

    const/4 v2, 0x3

    aput-object p2, v1, v2

    const/4 v2, 0x4

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->knLblˊᵔٴᵎـʽʿʿʻˋˎˆٴⁱᵎˑˉᴵٴᐧˏYWQrx()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v1, v2

    const/4 v2, 0x5

    aput-object v0, v1, v2

    const/4 v2, 0x6

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->fgYRyٴﾞיᐧˆיﹳˆˋᴵⁱʾʾᵔˈᐧBGkmx()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v1, v2

    iget-object v2, p0, Lcom/api/KeyAuth;->sessionid:Ljava/lang/String;

    const/4 v3, 0x7

    aput-object v2, v1, v3

    const/16 v2, 0x8

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->KeWxFʿʿᴵʿיˎʻﹶיⁱᐧpSjjd()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v1, v2

    iget-object v2, p0, Lcom/api/KeyAuth;->appname:Ljava/lang/String;

    const/16 v3, 0x9

    aput-object v2, v1, v3

    const/16 v2, 0xa

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->EsgAXˆﹶˆᵎˈᵎⁱⁱᵔـᵎˈﾞⁱᐧˎʼʽhNjYZ()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v1, v2

    iget-object v2, p0, Lcom/api/KeyAuth;->ownerid:Ljava/lang/String;

    const/16 v3, 0xb

    aput-object v2, v1, v3

    const-string v2, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->iRQXKʾˏٴʾـᐧــˏˈﹶﹳᐧᐧhKdyf()Ljava/lang/String;

    move-result-object v2

    invoke-direct {p0, v2, v1}, Lcom/api/KeyAuth;->sendPostRequest(Ljava/lang/String;[Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    .line 156
    .local v1, "response":Ljava/lang/String;
    if-eqz v1, :cond_2

    .line 158
    :try_start_0
    new-instance v2, Lorg/json/JSONObject;

    invoke-direct {v2, v1}, Lorg/json/JSONObject;-><init>(Ljava/lang/String;)V

    .line 160
    .local v2, "responseJSON":Lorg/json/JSONObject;
    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->bJpfOᵢﹶﾞˏﹳʽᴵˑᴵʽᴵﾞʿʿʾـJKmSR()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Lorg/json/JSONObject;->getBoolean(Ljava/lang/String;)Z

    move-result v3

    if-eqz v3, :cond_0

    .line 161
    new-instance v3, Lcom/api/UserData;

    invoke-direct {v3, v2}, Lcom/api/UserData;-><init>(Lorg/json/JSONObject;)V

    iput-object v3, p0, Lcom/api/KeyAuth;->userData:Lcom/api/UserData;

    .line 162
    if-eqz p3, :cond_1

    .line 163
    invoke-interface {p3, v3}, Lcom/api/KeyAuth$LoginCallback;->onSuccess(Lcom/api/UserData;)V

    goto :goto_0

    .line 166
    :cond_0
    if-eqz p3, :cond_1

    .line 167
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->BebElـᵢʼˈᵎʻʽⁱˑˏIQsWQ()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v4, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->NDLXiˎᵢʿʼⁱˑייˏʿיᵔﹳˋﾞˑⁱˈHsLJM()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v4}, Lorg/json/JSONObject;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-interface {p3, v3}, Lcom/api/KeyAuth$LoginCallback;->onFailure(Ljava/lang/String;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    .line 170
    .end local v2    # "responseJSON":Lorg/json/JSONObject;
    :catch_0
    move-exception v2

    .line 171
    .local v2, "e":Ljava/lang/Exception;
    invoke-virtual {v2}, Ljava/lang/Exception;->printStackTrace()V

    .line 172
    if-eqz p3, :cond_1

    .line 173
    invoke-virtual {v2}, Ljava/lang/Exception;->getMessage()Ljava/lang/String;

    move-result-object v3

    invoke-interface {p3, v3}, Lcom/api/KeyAuth$LoginCallback;->onFailure(Ljava/lang/String;)V

    .line 175
    .end local v2    # "e":Ljava/lang/Exception;
    :cond_1
    :goto_0
    goto :goto_1

    .line 177
    :cond_2
    if-eqz p3, :cond_3

    .line 178
    const-string v2, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->wWWZuﾞˎٴﹳˋיⁱʼᐧᐧˈᵎﹳـٴـˋٴˑٴvYRYs()Ljava/lang/String;

    move-result-object v2

    invoke-interface {p3, v2}, Lcom/api/KeyAuth$LoginCallback;->onFailure(Ljava/lang/String;)V

    .line 181
    :cond_3
    :goto_1
    return-void
.end method

.method public synthetic lambda$upgrade$2$KeyAuth(Ljava/lang/String;Ljava/lang/String;Lcom/api/KeyAuth$UpgradeCallback;)V
    .locals 5
    .param p1, "username"    # Ljava/lang/String;
    .param p2, "key"    # Ljava/lang/String;
    .param p3, "callback"    # Lcom/api/KeyAuth$UpgradeCallback;

    .line 191
    invoke-static {}, Lcom/api/HWID;->getHWID()Ljava/lang/String;

    move-result-object v0

    .line 193
    .local v0, "hwid":Ljava/lang/String;
    const/16 v1, 0xc

    new-array v1, v1, [Ljava/lang/String;

    const/4 v2, 0x0

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->hnXjnʼˊʽٴʽⁱיʾᐧˎᐧˎrhzCH()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v1, v2

    const/4 v2, 0x1

    aput-object p1, v1, v2

    const/4 v2, 0x2

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->eJDOqˈˏʼﹳﹳˎˊˆᵔᵎIGZDJ()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v1, v2

    const/4 v2, 0x3

    aput-object p2, v1, v2

    const/4 v2, 0x4

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->HucsnˈˉˏᴵʻᴵᐧˆʼʽnonMf()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v1, v2

    const/4 v2, 0x5

    aput-object v0, v1, v2

    const/4 v2, 0x6

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->GeIRm﻿ʿـᵔﾞʻⁱˎⁱᐧﹶʾˈˊﹳʽʿlYCSU()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v1, v2

    iget-object v2, p0, Lcom/api/KeyAuth;->sessionid:Ljava/lang/String;

    const/4 v3, 0x7

    aput-object v2, v1, v3

    const/16 v2, 0x8

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->hzvfzᴵᐧʽʼᐧˋᵎـᵔˏʿﾞـʽᴵˈˆʼRTEqZ()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v1, v2

    iget-object v2, p0, Lcom/api/KeyAuth;->appname:Ljava/lang/String;

    const/16 v3, 0x9

    aput-object v2, v1, v3

    const/16 v2, 0xa

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->RAhpDᵎـᵢʽـˋˆᵢיˈxlbGj()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v1, v2

    iget-object v2, p0, Lcom/api/KeyAuth;->ownerid:Ljava/lang/String;

    const/16 v3, 0xb

    aput-object v2, v1, v3

    const-string v2, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->rJjDWˑʼיˊⁱˉᐧˎᵎˑﹳEYijN()Ljava/lang/String;

    move-result-object v2

    invoke-direct {p0, v2, v1}, Lcom/api/KeyAuth;->sendPostRequest(Ljava/lang/String;[Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    .line 202
    .local v1, "response":Ljava/lang/String;
    if-eqz v1, :cond_2

    .line 204
    :try_start_0
    new-instance v2, Lorg/json/JSONObject;

    invoke-direct {v2, v1}, Lorg/json/JSONObject;-><init>(Ljava/lang/String;)V

    .line 206
    .local v2, "responseJSON":Lorg/json/JSONObject;
    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->SzRKRᵢˑﹳˊʼˈᐧʿᴵʽRanru()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Lorg/json/JSONObject;->getBoolean(Ljava/lang/String;)Z

    move-result v3

    if-eqz v3, :cond_0

    .line 207
    if-eqz p3, :cond_1

    .line 208
    invoke-interface {p3}, Lcom/api/KeyAuth$UpgradeCallback;->onSuccess()V

    goto :goto_0

    .line 211
    :cond_0
    if-eqz p3, :cond_1

    .line 212
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->LyDTeᵔʿˈˑᵢʾⁱʿᴵٴʻˏﾞʽˆAXabj()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v4, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->DgCcKʽˉᐧˎˏʾˎٴᴵⁱˈˏʾˉˑʻᵔˆˋﾞeVLcN()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v4}, Lorg/json/JSONObject;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-interface {p3, v3}, Lcom/api/KeyAuth$UpgradeCallback;->onFailure(Ljava/lang/String;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    .line 215
    .end local v2    # "responseJSON":Lorg/json/JSONObject;
    :catch_0
    move-exception v2

    .line 216
    .local v2, "e":Ljava/lang/Exception;
    invoke-virtual {v2}, Ljava/lang/Exception;->printStackTrace()V

    .line 217
    if-eqz p3, :cond_1

    .line 218
    invoke-virtual {v2}, Ljava/lang/Exception;->getMessage()Ljava/lang/String;

    move-result-object v3

    invoke-interface {p3, v3}, Lcom/api/KeyAuth$UpgradeCallback;->onFailure(Ljava/lang/String;)V

    .line 220
    .end local v2    # "e":Ljava/lang/Exception;
    :cond_1
    :goto_0
    goto :goto_1

    .line 222
    :cond_2
    if-eqz p3, :cond_3

    .line 223
    const-string v2, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->wcuneʼﹶٴʿˏﾞˈᴵٴˉˑˉʻʿDyzMd()Ljava/lang/String;

    move-result-object v2

    invoke-interface {p3, v2}, Lcom/api/KeyAuth$UpgradeCallback;->onFailure(Ljava/lang/String;)V

    .line 226
    :cond_3
    :goto_1
    return-void
.end method

.method public synthetic lambda$webhook$5$KeyAuth(Ljava/lang/String;Ljava/lang/String;Lcom/api/KeyAuth$WebhookCallback;)V
    .locals 5
    .param p1, "webid"    # Ljava/lang/String;
    .param p2, "param"    # Ljava/lang/String;
    .param p3, "callback"    # Lcom/api/KeyAuth$WebhookCallback;

    .line 323
    invoke-static {}, Lcom/api/HWID;->getHWID()Ljava/lang/String;

    move-result-object v0

    .line 325
    .local v0, "hwid":Ljava/lang/String;
    const/16 v1, 0xa

    new-array v1, v1, [Ljava/lang/String;

    const/4 v2, 0x0

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->JjlUvʿᵔˉʽⁱˎˈיﹶʻᴵᴵˎᵔuYmyf()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v1, v2

    const/4 v2, 0x1

    aput-object p1, v1, v2

    const/4 v2, 0x2

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->fTYGlⁱˈˏˈٴᴵיٴᵎˉᐧˑʾˏᴵˎRxerf()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v1, v2

    const/4 v2, 0x3

    aput-object p2, v1, v2

    const/4 v2, 0x4

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->XKGjqˆˊٴˆﹶﹶˋיᴵﹶˑʽᐧˋᴵˎˈˉـˆoOeEL()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v1, v2

    iget-object v2, p0, Lcom/api/KeyAuth;->sessionid:Ljava/lang/String;

    const/4 v3, 0x5

    aput-object v2, v1, v3

    const/4 v2, 0x6

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->tQEMhﹳᵔʼᐧٴʼˉᵢSXCMh()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v1, v2

    iget-object v2, p0, Lcom/api/KeyAuth;->appname:Ljava/lang/String;

    const/4 v3, 0x7

    aput-object v2, v1, v3

    const/16 v2, 0x8

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->cbVhSⁱﹳˆــᴵˉʼᵎﹳˈʼˏˉpBLRG()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v1, v2

    iget-object v2, p0, Lcom/api/KeyAuth;->ownerid:Ljava/lang/String;

    const/16 v3, 0x9

    aput-object v2, v1, v3

    const-string v2, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->dCpzqﾞʾﹳʻᴵـﹶˉʽᴵʾˊrLgSj()Ljava/lang/String;

    move-result-object v2

    invoke-direct {p0, v2, v1}, Lcom/api/KeyAuth;->sendPostRequest(Ljava/lang/String;[Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    .line 333
    .local v1, "response":Ljava/lang/String;
    if-eqz v1, :cond_2

    .line 335
    :try_start_0
    new-instance v2, Lorg/json/JSONObject;

    invoke-direct {v2, v1}, Lorg/json/JSONObject;-><init>(Ljava/lang/String;)V

    .line 337
    .local v2, "responseJSON":Lorg/json/JSONObject;
    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->txVJqᵢˑᵎⁱˉʻﹳˉʿⁱיˎﹶʿuvSZk()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Lorg/json/JSONObject;->getBoolean(Ljava/lang/String;)Z

    move-result v3

    if-eqz v3, :cond_0

    .line 338
    if-eqz p3, :cond_1

    .line 339
    invoke-interface {p3}, Lcom/api/KeyAuth$WebhookCallback;->onSuccess()V

    goto :goto_0

    .line 342
    :cond_0
    if-eqz p3, :cond_1

    .line 343
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->GgzgQـˊˆʼᵢﾞﹶٴᐧˏﾞˉﹶʻᴵˈˉᐧsyGIn()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v4, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->YIGRuʻיⁱʻʼﹳˑˑיʽʽˉʿˎghqeN()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v4}, Lorg/json/JSONObject;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-interface {p3, v3}, Lcom/api/KeyAuth$WebhookCallback;->onFailure(Ljava/lang/String;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    .line 346
    .end local v2    # "responseJSON":Lorg/json/JSONObject;
    :catch_0
    move-exception v2

    .line 347
    .local v2, "e":Ljava/lang/Exception;
    invoke-virtual {v2}, Ljava/lang/Exception;->printStackTrace()V

    .line 348
    if-eqz p3, :cond_1

    .line 349
    invoke-virtual {v2}, Ljava/lang/Exception;->getMessage()Ljava/lang/String;

    move-result-object v3

    invoke-interface {p3, v3}, Lcom/api/KeyAuth$WebhookCallback;->onFailure(Ljava/lang/String;)V

    .line 351
    .end local v2    # "e":Ljava/lang/Exception;
    :cond_1
    :goto_0
    goto :goto_1

    .line 353
    :cond_2
    if-eqz p3, :cond_3

    .line 354
    const-string v2, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->vJOjcˈᴵᵔﹳﾞˊٴᵔˋʻˈﾞˆיCwgbL()Ljava/lang/String;

    move-result-object v2

    invoke-interface {p3, v2}, Lcom/api/KeyAuth$WebhookCallback;->onFailure(Ljava/lang/String;)V

    .line 357
    :cond_3
    :goto_1
    return-void
.end method

.method public license(Ljava/lang/String;Lcom/api/KeyAuth$LicenseCallback;)V
    .locals 2
    .param p1, "key"    # Ljava/lang/String;
    .param p2, "callback"    # Lcom/api/KeyAuth$LicenseCallback;

    .line 230
    iget-boolean v0, p0, Lcom/api/KeyAuth;->initialized:Z

    if-nez v0, :cond_0

    .line 231
    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->yjofaˏᴵˏˏˎᵎˋˑᵢᵢˎˉﹳʼˋˎⁱˉˑˑʽˈkvZxP()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 232
    return-void

    .line 235
    :cond_0
    new-instance v0, Ljava/lang/Thread;

    new-instance v1, Lcom/api/-$$Lambda$KeyAuth$i676pfRaXgOjM6ss1fBQqs15eqA;

    invoke-direct {v1, p0, p1, p2}, Lcom/api/-$$Lambda$KeyAuth$i676pfRaXgOjM6ss1fBQqs15eqA;-><init>(Lcom/api/KeyAuth;Ljava/lang/String;Lcom/api/KeyAuth$LicenseCallback;)V

    invoke-direct {v0, v1}, Ljava/lang/Thread;-><init>(Ljava/lang/Runnable;)V

    .line 271
    invoke-virtual {v0}, Ljava/lang/Thread;->start()V

    .line 272
    return-void
.end method

.method public login(Ljava/lang/String;Ljava/lang/String;Lcom/api/KeyAuth$LoginCallback;)V
    .locals 2
    .param p1, "username"    # Ljava/lang/String;
    .param p2, "password"    # Ljava/lang/String;
    .param p3, "callback"    # Lcom/api/KeyAuth$LoginCallback;

    .line 139
    iget-boolean v0, p0, Lcom/api/KeyAuth;->initialized:Z

    if-nez v0, :cond_0

    .line 140
    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->WIwsoˋʾˆˑـˆˊᵔיᵔˊٴיwlkFi()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 141
    return-void

    .line 144
    :cond_0
    new-instance v0, Ljava/lang/Thread;

    new-instance v1, Lcom/api/-$$Lambda$KeyAuth$md10HFLIVF5PesGYhMDSifSlOG8;

    invoke-direct {v1, p0, p1, p2, p3}, Lcom/api/-$$Lambda$KeyAuth$md10HFLIVF5PesGYhMDSifSlOG8;-><init>(Lcom/api/KeyAuth;Ljava/lang/String;Ljava/lang/String;Lcom/api/KeyAuth$LoginCallback;)V

    invoke-direct {v0, v1}, Ljava/lang/Thread;-><init>(Ljava/lang/Runnable;)V

    .line 181
    invoke-virtual {v0}, Ljava/lang/Thread;->start()V

    .line 182
    return-void
.end method

.method public upgrade(Ljava/lang/String;Ljava/lang/String;Lcom/api/KeyAuth$UpgradeCallback;)V
    .locals 2
    .param p1, "username"    # Ljava/lang/String;
    .param p2, "key"    # Ljava/lang/String;
    .param p3, "callback"    # Lcom/api/KeyAuth$UpgradeCallback;

    .line 185
    iget-boolean v0, p0, Lcom/api/KeyAuth;->initialized:Z

    if-nez v0, :cond_0

    .line 186
    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->JTrhhʾٴˈʼʻʿˋﹶﾞᵎˉٴʻﹳᵎᵔᴵᴵPKAWe()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 187
    return-void

    .line 190
    :cond_0
    new-instance v0, Ljava/lang/Thread;

    new-instance v1, Lcom/api/-$$Lambda$KeyAuth$XvBNhpuY9WPrLbBoum7yHVkzTaI;

    invoke-direct {v1, p0, p1, p2, p3}, Lcom/api/-$$Lambda$KeyAuth$XvBNhpuY9WPrLbBoum7yHVkzTaI;-><init>(Lcom/api/KeyAuth;Ljava/lang/String;Ljava/lang/String;Lcom/api/KeyAuth$UpgradeCallback;)V

    invoke-direct {v0, v1}, Ljava/lang/Thread;-><init>(Ljava/lang/Runnable;)V

    .line 226
    invoke-virtual {v0}, Ljava/lang/Thread;->start()V

    .line 227
    return-void
.end method

.method public webhook(Ljava/lang/String;Ljava/lang/String;Lcom/api/KeyAuth$WebhookCallback;)V
    .locals 2
    .param p1, "webid"    # Ljava/lang/String;
    .param p2, "param"    # Ljava/lang/String;
    .param p3, "callback"    # Lcom/api/KeyAuth$WebhookCallback;

    .line 317
    iget-boolean v0, p0, Lcom/api/KeyAuth;->initialized:Z

    if-nez v0, :cond_0

    .line 318
    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->hNZmQᵎٴˋᵔʾᵔʿˉـʾYYgmi()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 319
    return-void

    .line 322
    :cond_0
    new-instance v0, Ljava/lang/Thread;

    new-instance v1, Lcom/api/-$$Lambda$KeyAuth$BAbtE5Xwhh4t8v3QqkiIgilKtc4;

    invoke-direct {v1, p0, p1, p2, p3}, Lcom/api/-$$Lambda$KeyAuth$BAbtE5Xwhh4t8v3QqkiIgilKtc4;-><init>(Lcom/api/KeyAuth;Ljava/lang/String;Ljava/lang/String;Lcom/api/KeyAuth$WebhookCallback;)V

    invoke-direct {v0, v1}, Ljava/lang/Thread;-><init>(Ljava/lang/Runnable;)V

    .line 357
    invoke-virtual {v0}, Ljava/lang/Thread;->start()V

    .line 358
    return-void
.end method
