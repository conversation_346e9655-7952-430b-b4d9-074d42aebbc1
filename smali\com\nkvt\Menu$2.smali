.class Lcom/nkvt/Menu$2;
.super Ljava/lang/Object;
.source "Menu.java"

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/nkvt/Menu;->onCreateTemplate()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lcom/nkvt/Menu;

.field final synthetic val$container_menu:Landroid/widget/LinearLayout;

.field final synthetic val$icon_cheat:Lcom/nkvt/ImageBase64;

.field final synthetic val$inject_close:Landroid/widget/Button;


# direct methods
.method constructor <init>(Lcom/nkvt/Menu;Landroid/widget/Button;Lcom/nkvt/ImageBase64;Landroid/widget/LinearLayout;)V
    .locals 0
    .param p1, "this$0"    # Lcom/nkvt/Menu;

    .line 183
    iput-object p1, p0, Lcom/nkvt/Menu$2;->this$0:Lcom/nkvt/Menu;

    iput-object p2, p0, Lcom/nkvt/Menu$2;->val$inject_close:Landroid/widget/Button;

    iput-object p3, p0, Lcom/nkvt/Menu$2;->val$icon_cheat:Lcom/nkvt/ImageBase64;

    iput-object p4, p0, Lcom/nkvt/Menu$2;->val$container_menu:Landroid/widget/LinearLayout;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 2
    .param p1, "view"    # Landroid/view/View;

    .line 186
    iget-object v0, p0, Lcom/nkvt/Menu$2;->this$0:Lcom/nkvt/Menu;

    invoke-static {v0}, Lcom/nkvt/Menu;->access$000(Lcom/nkvt/Menu;)I

    move-result v0

    const/4 v1, 0x1

    if-nez v0, :cond_1

    .line 187
    iget-object v0, p0, Lcom/nkvt/Menu$2;->this$0:Lcom/nkvt/Menu;

    invoke-static {v0}, Lcom/nkvt/Menu;->access$100(Lcom/nkvt/Menu;)I

    move-result v0

    if-nez v0, :cond_0

    goto :goto_0

    .line 189
    :cond_0
    iget-object v0, p0, Lcom/nkvt/Menu$2;->this$0:Lcom/nkvt/Menu;

    invoke-static {v0}, Lcom/nkvt/Menu;->access$100(Lcom/nkvt/Menu;)I

    move-result v0

    if-ne v0, v1, :cond_2

    .line 190
    iget-object v0, p0, Lcom/nkvt/Menu$2;->this$0:Lcom/nkvt/Menu;

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->gtvsqˈʽˈᵔᴵᐧʾᵎיˏˋʽᐧـٴLMkun()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/nkvt/Menu;->access$200(Lcom/nkvt/Menu;Ljava/lang/String;)Z

    move-result v0

    if-eqz v0, :cond_2

    .line 191
    invoke-static {}, Lcom/nkvt/Menu;->Init()V

    .line 193
    iget-object v0, p0, Lcom/nkvt/Menu$2;->val$inject_close:Landroid/widget/Button;

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->UCqPTʼﹳˋˊᵎﹶـᵎˈʻᵔᵎיʽᵔIZzvG()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    .line 195
    iget-object v0, p0, Lcom/nkvt/Menu$2;->this$0:Lcom/nkvt/Menu;

    invoke-static {v0}, Lcom/nkvt/Menu;->access$008(Lcom/nkvt/Menu;)I

    goto :goto_0

    .line 198
    :cond_1
    iget-object v0, p0, Lcom/nkvt/Menu$2;->this$0:Lcom/nkvt/Menu;

    invoke-static {v0}, Lcom/nkvt/Menu;->access$000(Lcom/nkvt/Menu;)I

    move-result v0

    if-ne v0, v1, :cond_2

    .line 199
    iget-object v0, p0, Lcom/nkvt/Menu$2;->val$icon_cheat:Lcom/nkvt/ImageBase64;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Lcom/nkvt/ImageBase64;->setVisibility(I)V

    .line 200
    iget-object v0, p0, Lcom/nkvt/Menu$2;->val$container_menu:Landroid/widget/LinearLayout;

    const/16 v1, 0x8

    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->setVisibility(I)V

    .line 202
    :cond_2
    :goto_0
    return-void
.end method
