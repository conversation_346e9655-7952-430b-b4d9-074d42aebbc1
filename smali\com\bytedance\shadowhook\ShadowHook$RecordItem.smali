.class public final enum Lcom/bytedance/shadowhook/ShadowHook$RecordItem;
.super Ljava/lang/Enum;
.source "ShadowHook.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/shadowhook/ShadowHook;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4019
    name = "RecordItem"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/bytedance/shadowhook/ShadowHook$RecordItem;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

.field public static final enum BACKUP_LEN:Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

.field public static final enum CALLER_LIB_NAME:Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

.field public static final enum ERRNO:Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

.field public static final enum LIB_NAME:Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

.field public static final enum NEW_ADDR:Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

.field public static final enum OP:Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

.field public static final enum STUB:Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

.field public static final enum SYM_ADDR:Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

.field public static final enum SYM_NAME:Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

.field public static final enum TIMESTAMP:Lcom/bytedance/shadowhook/ShadowHook$RecordItem;


# direct methods
.method private static synthetic $values()[Lcom/bytedance/shadowhook/ShadowHook$RecordItem;
    .locals 3

    .line 280
    const/16 v0, 0xa

    new-array v0, v0, [Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    sget-object v1, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;->TIMESTAMP:Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;->CALLER_LIB_NAME:Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;->OP:Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    sget-object v1, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;->LIB_NAME:Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    sget-object v1, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;->SYM_NAME:Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    const/4 v2, 0x4

    aput-object v1, v0, v2

    sget-object v1, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;->SYM_ADDR:Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    const/4 v2, 0x5

    aput-object v1, v0, v2

    sget-object v1, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;->NEW_ADDR:Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    const/4 v2, 0x6

    aput-object v1, v0, v2

    sget-object v1, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;->BACKUP_LEN:Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    const/4 v2, 0x7

    aput-object v1, v0, v2

    sget-object v1, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;->ERRNO:Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    const/16 v2, 0x8

    aput-object v1, v0, v2

    sget-object v1, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;->STUB:Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    const/16 v2, 0x9

    aput-object v1, v0, v2

    return-object v0
.end method

.method static constructor <clinit>()V
    .locals 3

    .line 281
    new-instance v0, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->jzJQFᵔˈᵎʾᴵˑˉˉˎˈSdQml()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;->TIMESTAMP:Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    .line 282
    new-instance v0, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->IFkHLˏʽˑˎﹶˏᴵᵎיʻˋˋﹶﹶᴵˋᵢʾKujEj()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x1

    invoke-direct {v0, v1, v2}, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;->CALLER_LIB_NAME:Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    .line 283
    new-instance v0, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->bWBXdˊˉˎˉⁱˉᐧˉיﾞייⁱˑʾיlJuFB()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x2

    invoke-direct {v0, v1, v2}, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;->OP:Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    .line 284
    new-instance v0, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->NCltNˈᐧˋⁱⁱˆˋـﹳﹳיʽﾞﾞvJWGz()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x3

    invoke-direct {v0, v1, v2}, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;->LIB_NAME:Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    .line 285
    new-instance v0, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->mENCpˉٴⁱʻʻˑᐧʾﾞʼᵢʿˊᴵqSTWJ()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x4

    invoke-direct {v0, v1, v2}, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;->SYM_NAME:Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    .line 286
    new-instance v0, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->ihmZFˑיᐧˎᵎʾˏˆʽיˋʾˋᵢyQElC()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x5

    invoke-direct {v0, v1, v2}, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;->SYM_ADDR:Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    .line 287
    new-instance v0, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->QHwOOٴـᵔʿᵎﹳᵔˋⁱʽʻـˈˆʻʿˎhjTAE()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x6

    invoke-direct {v0, v1, v2}, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;->NEW_ADDR:Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    .line 288
    new-instance v0, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->yjhrVʾˊʼˎʾיˋᵎˉייⁱˆˈٴˈⁱᵢNeUKg()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x7

    invoke-direct {v0, v1, v2}, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;->BACKUP_LEN:Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    .line 289
    new-instance v0, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->PHjLOᵎʾˉיˈˋـᵢˊʿˎˊʻᐧpEvmf()Ljava/lang/String;

    move-result-object v1

    const/16 v2, 0x8

    invoke-direct {v0, v1, v2}, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;->ERRNO:Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    .line 290
    new-instance v0, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->pToBWⁱˎˎᵎˎˏיʼʽʿˏﹶˑᵢˉיـˏᐧEcYlA()Ljava/lang/String;

    move-result-object v1

    const/16 v2, 0x9

    invoke-direct {v0, v1, v2}, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;->STUB:Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    .line 280
    invoke-static {}, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;->$values()[Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    move-result-object v0

    sput-object v0, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;->$VALUES:[Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 280
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lcom/bytedance/shadowhook/ShadowHook$RecordItem;
    .locals 1
    .param p0, "name"    # Ljava/lang/String;

    .line 280
    const-class v0, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object v0

    check-cast v0, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    return-object v0
.end method

.method public static values()[Lcom/bytedance/shadowhook/ShadowHook$RecordItem;
    .locals 1

    .line 280
    sget-object v0, Lcom/bytedance/shadowhook/ShadowHook$RecordItem;->$VALUES:[Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    invoke-virtual {v0}, [Lcom/bytedance/shadowhook/ShadowHook$RecordItem;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/bytedance/shadowhook/ShadowHook$RecordItem;

    return-object v0
.end method
