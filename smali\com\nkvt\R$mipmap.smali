.class public final Lcom/nkvt/R$mipmap;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/nkvt/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "mipmap"
.end annotation


# static fields
.field public static final ic_launcher:I = 0x7f040000

.field public static final ic_launcher_adaptive_bac1k:I = 0x7f040001

.field public static final ic_launcher_adaptive_back:I = 0x7f040002

.field public static final ic_launcher_adaptive_fore:I = 0x7f040003

.field public static final ic_launcher_round:I = 0x7f040004


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
