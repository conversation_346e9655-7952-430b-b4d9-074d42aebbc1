.class public final synthetic Lcom/nkvt/-$$Lambda$Login$rcAkLK4T-RYk-MhJTijPApkNF0k;
.super Ljava/lang/Object;
.source "lambda"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic f$0:Lcom/nkvt/Login;

.field public final synthetic f$1:Landroid/widget/EditText;

.field public final synthetic f$2:Landroid/widget/Button;


# direct methods
.method public synthetic constructor <init>(Lcom/nkvt/Login;Landroid/widget/EditText;Landroid/widget/Button;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/nkvt/-$$Lambda$Login$rcAkLK4T-RYk-MhJTijPApkNF0k;->f$0:Lcom/nkvt/Login;

    iput-object p2, p0, Lcom/nkvt/-$$Lambda$Login$rcAkLK4T-RYk-MhJTijPApkNF0k;->f$1:Landroid/widget/EditText;

    iput-object p3, p0, Lcom/nkvt/-$$Lambda$Login$rcAkLK4T-RYk-MhJTijPApkNF0k;->f$2:Landroid/widget/Button;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 3

    iget-object v0, p0, Lcom/nkvt/-$$Lambda$Login$rcAkLK4T-RYk-MhJTijPApkNF0k;->f$0:Lcom/nkvt/Login;

    iget-object v1, p0, Lcom/nkvt/-$$Lambda$Login$rcAkLK4T-RYk-MhJTijPApkNF0k;->f$1:Landroid/widget/EditText;

    iget-object v2, p0, Lcom/nkvt/-$$Lambda$Login$rcAkLK4T-RYk-MhJTijPApkNF0k;->f$2:Landroid/widget/Button;

    invoke-virtual {v0, v1, v2, p1}, Lcom/nkvt/Login;->lambda$Init$3$Login(Landroid/widget/EditText;Landroid/widget/Button;Landroid/view/View;)V

    return-void
.end method
