.class public Lcom/nkvt/DrawView;
.super Landroid/view/View;
.source "DrawView.java"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field FPS:I

.field mBitMapPaint:Landroid/graphics/Paint;

.field private mContext:Landroid/content/Context;

.field mFilledPaint:Landroid/graphics/Paint;

.field mRectPaint1:Landroid/graphics/Paint;

.field mRectPaint2:Landroid/graphics/Paint;

.field mStrokePaint:Landroid/graphics/Paint;

.field mTextPaint:Landroid/graphics/Paint;

.field mThread:Ljava/lang/Thread;

.field sleepTime:J

.field time:Ljava/util/Date;


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 2
    .param p1, "context"    # Landroid/content/Context;

    .line 35
    const/4 v0, 0x0

    const/4 v1, 0x0

    invoke-direct {p0, p1, v0, v1}, Landroid/view/View;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 28
    const/16 v0, 0xf0

    iput v0, p0, Lcom/nkvt/DrawView;->FPS:I

    .line 36
    invoke-virtual {p0}, Lcom/nkvt/DrawView;->InitializePaints()V

    .line 37
    invoke-virtual {p0, v1}, Lcom/nkvt/DrawView;->setFocusableInTouchMode(Z)V

    .line 38
    invoke-virtual {p0, v1}, Lcom/nkvt/DrawView;->setBackgroundColor(I)V

    .line 39
    new-instance v0, Ljava/util/Date;

    invoke-direct {v0}, Ljava/util/Date;-><init>()V

    iput-object v0, p0, Lcom/nkvt/DrawView;->time:Ljava/util/Date;

    .line 40
    iget v0, p0, Lcom/nkvt/DrawView;->FPS:I

    const/16 v1, 0x3e8

    div-int/2addr v1, v0

    int-to-long v0, v1

    iput-wide v0, p0, Lcom/nkvt/DrawView;->sleepTime:J

    .line 41
    new-instance v0, Ljava/lang/Thread;

    invoke-direct {v0, p0}, Ljava/lang/Thread;-><init>(Ljava/lang/Runnable;)V

    iput-object v0, p0, Lcom/nkvt/DrawView;->mThread:Ljava/lang/Thread;

    .line 42
    invoke-virtual {v0}, Ljava/lang/Thread;->start()V

    .line 43
    iput-object p1, p0, Lcom/nkvt/DrawView;->mContext:Landroid/content/Context;

    .line 45
    return-void
.end method


# virtual methods
.method public ClearCanvas(Landroid/graphics/Canvas;)V
    .locals 2
    .param p1, "cvs"    # Landroid/graphics/Canvas;

    .line 107
    sget-object v0, Landroid/graphics/PorterDuff$Mode;->CLEAR:Landroid/graphics/PorterDuff$Mode;

    const/4 v1, 0x0

    invoke-virtual {p1, v1, v0}, Landroid/graphics/Canvas;->drawColor(ILandroid/graphics/PorterDuff$Mode;)V

    .line 108
    return-void
.end method

.method public DrawCircle(Landroid/graphics/Canvas;IIIIFFFF)V
    .locals 2
    .param p1, "cvs"    # Landroid/graphics/Canvas;
    .param p2, "a"    # I
    .param p3, "r"    # I
    .param p4, "g"    # I
    .param p5, "b"    # I
    .param p6, "stroke"    # F
    .param p7, "posX"    # F
    .param p8, "posY"    # F
    .param p9, "radius"    # F

    .line 195
    iget-object v0, p0, Lcom/nkvt/DrawView;->mStrokePaint:Landroid/graphics/Paint;

    invoke-static {p3, p4, p5}, Landroid/graphics/Color;->rgb(III)I

    move-result v1

    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setColor(I)V

    .line 196
    iget-object v0, p0, Lcom/nkvt/DrawView;->mStrokePaint:Landroid/graphics/Paint;

    invoke-virtual {v0, p2}, Landroid/graphics/Paint;->setAlpha(I)V

    .line 197
    iget-object v0, p0, Lcom/nkvt/DrawView;->mStrokePaint:Landroid/graphics/Paint;

    invoke-virtual {v0, p6}, Landroid/graphics/Paint;->setStrokeWidth(F)V

    .line 198
    iget-object v0, p0, Lcom/nkvt/DrawView;->mStrokePaint:Landroid/graphics/Paint;

    invoke-virtual {p1, p7, p8, p9, v0}, Landroid/graphics/Canvas;->drawCircle(FFFLandroid/graphics/Paint;)V

    .line 199
    return-void
.end method

.method public DrawFilledCircle(Landroid/graphics/Canvas;IIIIFFF)V
    .locals 2
    .param p1, "cvs"    # Landroid/graphics/Canvas;
    .param p2, "a"    # I
    .param p3, "r"    # I
    .param p4, "g"    # I
    .param p5, "b"    # I
    .param p6, "posX"    # F
    .param p7, "posY"    # F
    .param p8, "radius"    # F

    .line 203
    iget-object v0, p0, Lcom/nkvt/DrawView;->mFilledPaint:Landroid/graphics/Paint;

    invoke-static {p3, p4, p5}, Landroid/graphics/Color;->rgb(III)I

    move-result v1

    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setColor(I)V

    .line 204
    iget-object v0, p0, Lcom/nkvt/DrawView;->mFilledPaint:Landroid/graphics/Paint;

    invoke-virtual {v0, p2}, Landroid/graphics/Paint;->setAlpha(I)V

    .line 205
    iget-object v0, p0, Lcom/nkvt/DrawView;->mFilledPaint:Landroid/graphics/Paint;

    invoke-virtual {p1, p6, p7, p8, v0}, Landroid/graphics/Canvas;->drawCircle(FFFLandroid/graphics/Paint;)V

    .line 206
    return-void
.end method

.method public DrawFilledRect(Landroid/graphics/Canvas;IIIIFFFF)V
    .locals 9
    .param p1, "cvs"    # Landroid/graphics/Canvas;
    .param p2, "a"    # I
    .param p3, "r"    # I
    .param p4, "g"    # I
    .param p5, "b"    # I
    .param p6, "x"    # F
    .param p7, "y"    # F
    .param p8, "width"    # F
    .param p9, "height"    # F

    .line 239
    move-object v0, p0

    iget-object v1, v0, Lcom/nkvt/DrawView;->mFilledPaint:Landroid/graphics/Paint;

    invoke-static {p3, p4, p5}, Landroid/graphics/Color;->rgb(III)I

    move-result v2

    invoke-virtual {v1, v2}, Landroid/graphics/Paint;->setColor(I)V

    .line 241
    iget-object v1, v0, Lcom/nkvt/DrawView;->mFilledPaint:Landroid/graphics/Paint;

    const/16 v2, 0x46

    invoke-virtual {v1, v2}, Landroid/graphics/Paint;->setAlpha(I)V

    .line 242
    add-float v6, p6, p8

    add-float v7, p7, p9

    iget-object v8, v0, Lcom/nkvt/DrawView;->mFilledPaint:Landroid/graphics/Paint;

    move-object v3, p1

    move v4, p6

    move/from16 v5, p7

    invoke-virtual/range {v3 .. v8}, Landroid/graphics/Canvas;->drawRect(FFFFLandroid/graphics/Paint;)V

    .line 243
    return-void
.end method

.method public DrawFilledRectInfo(Landroid/graphics/Canvas;IIIIFFFF)V
    .locals 9
    .param p1, "cvs"    # Landroid/graphics/Canvas;
    .param p2, "a"    # I
    .param p3, "r"    # I
    .param p4, "g"    # I
    .param p5, "b"    # I
    .param p6, "x"    # F
    .param p7, "y"    # F
    .param p8, "width"    # F
    .param p9, "height"    # F

    .line 247
    move-object v0, p0

    iget-object v1, v0, Lcom/nkvt/DrawView;->mFilledPaint:Landroid/graphics/Paint;

    invoke-static {p3, p4, p5}, Landroid/graphics/Color;->rgb(III)I

    move-result v2

    invoke-virtual {v1, v2}, Landroid/graphics/Paint;->setColor(I)V

    .line 249
    iget-object v1, v0, Lcom/nkvt/DrawView;->mFilledPaint:Landroid/graphics/Paint;

    move v2, p2

    invoke-virtual {v1, p2}, Landroid/graphics/Paint;->setAlpha(I)V

    .line 250
    add-float v6, p6, p8

    add-float v7, p7, p9

    iget-object v8, v0, Lcom/nkvt/DrawView;->mFilledPaint:Landroid/graphics/Paint;

    move-object v3, p1

    move v4, p6

    move/from16 v5, p7

    invoke-virtual/range {v3 .. v8}, Landroid/graphics/Canvas;->drawRect(FFFFLandroid/graphics/Paint;)V

    .line 251
    return-void
.end method

.method public DrawGradientRect(Landroid/graphics/Canvas;IIIIIIIIFFFF)V
    .locals 10
    .param p1, "canvas"    # Landroid/graphics/Canvas;
    .param p2, "startA"    # I
    .param p3, "startR"    # I
    .param p4, "startG"    # I
    .param p5, "startB"    # I
    .param p6, "endA"    # I
    .param p7, "endR"    # I
    .param p8, "endG"    # I
    .param p9, "endB"    # I
    .param p10, "x"    # F
    .param p11, "y"    # F
    .param p12, "width"    # F
    .param p13, "height"    # F

    .line 120
    new-instance v0, Landroid/graphics/Paint;

    invoke-direct {v0}, Landroid/graphics/Paint;-><init>()V

    move-object v8, v0

    .line 121
    .local v8, "paint":Landroid/graphics/Paint;
    new-instance v9, Landroid/graphics/LinearGradient;

    add-float v3, p10, p12

    add-float v4, p11, p13

    .line 123
    invoke-static {p2, p3, p4, p5}, Landroid/graphics/Color;->argb(IIII)I

    move-result v5

    .line 124
    invoke-static/range {p6 .. p9}, Landroid/graphics/Color;->argb(IIII)I

    move-result v6

    sget-object v7, Landroid/graphics/Shader$TileMode;->CLAMP:Landroid/graphics/Shader$TileMode;

    move-object v0, v9

    move/from16 v1, p10

    move/from16 v2, p11

    invoke-direct/range {v0 .. v7}, Landroid/graphics/LinearGradient;-><init>(FFFFIILandroid/graphics/Shader$TileMode;)V

    move-object v6, v9

    .line 127
    .local v6, "gradient":Landroid/graphics/LinearGradient;
    invoke-virtual {v8, v6}, Landroid/graphics/Paint;->setShader(Landroid/graphics/Shader;)Landroid/graphics/Shader;

    .line 128
    add-float v3, p10, p12

    add-float v4, p11, p13

    move-object v0, p1

    move-object v5, v8

    invoke-virtual/range {v0 .. v5}, Landroid/graphics/Canvas;->drawRect(FFFFLandroid/graphics/Paint;)V

    .line 129
    return-void
.end method

.method public DrawLine(Landroid/graphics/Canvas;IIIIFFFFF)V
    .locals 10
    .param p1, "cvs"    # Landroid/graphics/Canvas;
    .param p2, "a"    # I
    .param p3, "r"    # I
    .param p4, "g"    # I
    .param p5, "b"    # I
    .param p6, "lineWidth"    # F
    .param p7, "fromX"    # F
    .param p8, "fromY"    # F
    .param p9, "toX"    # F
    .param p10, "toY"    # F

    .line 111
    move-object v0, p0

    iget-object v1, v0, Lcom/nkvt/DrawView;->mStrokePaint:Landroid/graphics/Paint;

    invoke-static {p3, p4, p5}, Landroid/graphics/Color;->rgb(III)I

    move-result v2

    invoke-virtual {v1, v2}, Landroid/graphics/Paint;->setColor(I)V

    .line 112
    iget-object v1, v0, Lcom/nkvt/DrawView;->mStrokePaint:Landroid/graphics/Paint;

    move v2, p2

    invoke-virtual {v1, p2}, Landroid/graphics/Paint;->setAlpha(I)V

    .line 113
    iget-object v1, v0, Lcom/nkvt/DrawView;->mStrokePaint:Landroid/graphics/Paint;

    move/from16 v3, p6

    invoke-virtual {v1, v3}, Landroid/graphics/Paint;->setStrokeWidth(F)V

    .line 114
    iget-object v9, v0, Lcom/nkvt/DrawView;->mStrokePaint:Landroid/graphics/Paint;

    move-object v4, p1

    move/from16 v5, p7

    move/from16 v6, p8

    move/from16 v7, p9

    move/from16 v8, p10

    invoke-virtual/range {v4 .. v9}, Landroid/graphics/Canvas;->drawLine(FFFFLandroid/graphics/Paint;)V

    .line 115
    return-void
.end method

.method public DrawRect(Landroid/graphics/Canvas;IIIIIFFFF)V
    .locals 10
    .param p1, "cvs"    # Landroid/graphics/Canvas;
    .param p2, "a"    # I
    .param p3, "r"    # I
    .param p4, "g"    # I
    .param p5, "b"    # I
    .param p6, "stroke"    # I
    .param p7, "x"    # F
    .param p8, "y"    # F
    .param p9, "width"    # F
    .param p10, "height"    # F

    .line 217
    move-object v0, p0

    iget-object v1, v0, Lcom/nkvt/DrawView;->mStrokePaint:Landroid/graphics/Paint;

    move/from16 v2, p6

    int-to-float v3, v2

    invoke-virtual {v1, v3}, Landroid/graphics/Paint;->setStrokeWidth(F)V

    .line 218
    iget-object v1, v0, Lcom/nkvt/DrawView;->mStrokePaint:Landroid/graphics/Paint;

    invoke-static {p3, p4, p5}, Landroid/graphics/Color;->rgb(III)I

    move-result v3

    invoke-virtual {v1, v3}, Landroid/graphics/Paint;->setColor(I)V

    .line 219
    iget-object v1, v0, Lcom/nkvt/DrawView;->mStrokePaint:Landroid/graphics/Paint;

    move v3, p2

    invoke-virtual {v1, p2}, Landroid/graphics/Paint;->setAlpha(I)V

    .line 220
    add-float v7, p7, p9

    add-float v8, p8, p10

    iget-object v9, v0, Lcom/nkvt/DrawView;->mStrokePaint:Landroid/graphics/Paint;

    move-object v4, p1

    move/from16 v5, p7

    move/from16 v6, p8

    invoke-virtual/range {v4 .. v9}, Landroid/graphics/Canvas;->drawRect(FFFFLandroid/graphics/Paint;)V

    .line 221
    return-void
.end method

.method public DrawRoundRect(Landroid/graphics/Canvas;IIIIFIIFFFF)V
    .locals 13
    .param p1, "cvs"    # Landroid/graphics/Canvas;
    .param p2, "a"    # I
    .param p3, "r"    # I
    .param p4, "g"    # I
    .param p5, "b"    # I
    .param p6, "stroke"    # F
    .param p7, "rx"    # I
    .param p8, "ry"    # I
    .param p9, "x"    # F
    .param p10, "y"    # F
    .param p11, "width"    # F
    .param p12, "height"    # F

    .line 209
    move-object v0, p0

    iget-object v1, v0, Lcom/nkvt/DrawView;->mStrokePaint:Landroid/graphics/Paint;

    move/from16 v2, p6

    invoke-virtual {v1, v2}, Landroid/graphics/Paint;->setStrokeWidth(F)V

    .line 210
    iget-object v1, v0, Lcom/nkvt/DrawView;->mStrokePaint:Landroid/graphics/Paint;

    invoke-static/range {p3 .. p5}, Landroid/graphics/Color;->rgb(III)I

    move-result v3

    invoke-virtual {v1, v3}, Landroid/graphics/Paint;->setColor(I)V

    .line 211
    iget-object v1, v0, Lcom/nkvt/DrawView;->mStrokePaint:Landroid/graphics/Paint;

    move v3, p2

    invoke-virtual {v1, p2}, Landroid/graphics/Paint;->setAlpha(I)V

    .line 212
    add-float v7, p9, p11

    add-float v8, p10, p12

    move/from16 v1, p7

    int-to-float v9, v1

    move/from16 v12, p8

    int-to-float v10, v12

    iget-object v11, v0, Lcom/nkvt/DrawView;->mStrokePaint:Landroid/graphics/Paint;

    move-object v4, p1

    move/from16 v5, p9

    move/from16 v6, p10

    invoke-virtual/range {v4 .. v11}, Landroid/graphics/Canvas;->drawRoundRect(FFFFFFLandroid/graphics/Paint;)V

    .line 213
    return-void
.end method

.method public DrawText(Landroid/graphics/Canvas;IIIIFLjava/lang/String;FFF)V
    .locals 12
    .param p1, "cvs"    # Landroid/graphics/Canvas;
    .param p2, "a"    # I
    .param p3, "r"    # I
    .param p4, "g"    # I
    .param p5, "b"    # I
    .param p6, "stroke"    # F
    .param p7, "txt"    # Ljava/lang/String;
    .param p8, "posX"    # F
    .param p9, "posY"    # F
    .param p10, "size"    # F

    move-object v1, p1

    .line 134
    :try_start_0
    new-instance v0, Landroid/text/TextPaint;

    invoke-direct {v0}, Landroid/text/TextPaint;-><init>()V

    .line 135
    .local v0, "textPaint":Landroid/text/TextPaint;
    invoke-static/range {p2 .. p5}, Landroid/graphics/Color;->argb(IIII)I

    move-result v2

    invoke-virtual {v0, v2}, Landroid/text/TextPaint;->setColor(I)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_2

    .line 136
    move/from16 v10, p10

    :try_start_1
    invoke-virtual {v0, v10}, Landroid/text/TextPaint;->setTextSize(F)V

    .line 137
    const/4 v2, 0x1

    invoke-virtual {v0, v2}, Landroid/text/TextPaint;->setAntiAlias(Z)V

    .line 138
    invoke-virtual {v0, v2}, Landroid/text/TextPaint;->setSubpixelText(Z)V

    .line 139
    sget-object v3, Landroid/graphics/Paint$Style;->FILL:Landroid/graphics/Paint$Style;

    invoke-virtual {v0, v3}, Landroid/text/TextPaint;->setStyle(Landroid/graphics/Paint$Style;)V

    .line 140
    const/4 v3, 0x0

    invoke-virtual {v0, v3}, Landroid/text/TextPaint;->setTypeface(Landroid/graphics/Typeface;)Landroid/graphics/Typeface;

    .line 142
    sget v3, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v4, 0x15

    if-lt v3, v4, :cond_0

    .line 143
    invoke-virtual {v0, v2}, Landroid/text/TextPaint;->setElegantTextHeight(Z)V

    .line 144
    const-string v2, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->DaPLgיˉˉˊʼٴᵎᐧⁱᵔˈᴵﾞˋᵢﹶTcyxn()Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Ljava/util/Locale;->forLanguageTag(Ljava/lang/String;)Ljava/util/Locale;

    move-result-object v2

    invoke-virtual {v0, v2}, Landroid/text/TextPaint;->setTextLocale(Ljava/util/Locale;)V

    .line 148
    :cond_0
    new-instance v11, Landroid/text/StaticLayout;

    const/16 v5, 0x2710

    sget-object v6, Landroid/text/Layout$Alignment;->ALIGN_NORMAL:Landroid/text/Layout$Alignment;

    const/high16 v7, 0x3f800000    # 1.0f

    const/4 v8, 0x0

    const/4 v9, 0x0

    move-object v2, v11

    move-object/from16 v3, p7

    move-object v4, v0

    invoke-direct/range {v2 .. v9}, Landroid/text/StaticLayout;-><init>(Ljava/lang/CharSequence;Landroid/text/TextPaint;ILandroid/text/Layout$Alignment;FFZ)V

    move-object v2, v11

    .line 158
    .local v2, "layout":Landroid/text/StaticLayout;
    invoke-virtual {p1}, Landroid/graphics/Canvas;->save()I

    .line 159
    invoke-virtual {v2}, Landroid/text/StaticLayout;->getHeight()I

    move-result v3
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_1

    int-to-float v3, v3

    sub-float v3, p9, v3

    move/from16 v4, p8

    :try_start_2
    invoke-virtual {p1, v4, v3}, Landroid/graphics/Canvas;->translate(FF)V

    .line 160
    invoke-virtual {v2, p1}, Landroid/text/StaticLayout;->draw(Landroid/graphics/Canvas;)V

    .line 161
    invoke-virtual {p1}, Landroid/graphics/Canvas;->restore()V
    :try_end_2
    .catch Ljava/lang/Exception; {:try_start_2 .. :try_end_2} :catch_0

    .end local v0    # "textPaint":Landroid/text/TextPaint;
    .end local v2    # "layout":Landroid/text/StaticLayout;
    goto :goto_0

    .line 163
    :catch_0
    move-exception v0

    goto :goto_0

    :catch_1
    move-exception v0

    move/from16 v4, p8

    goto :goto_0

    :catch_2
    move-exception v0

    move/from16 v4, p8

    move/from16 v10, p10

    :goto_0
    nop

    .line 164
    return-void
.end method

.method public DrawText2(Landroid/graphics/Canvas;IIIILjava/lang/String;FFF)V
    .locals 4
    .param p1, "cvs"    # Landroid/graphics/Canvas;
    .param p2, "a"    # I
    .param p3, "r"    # I
    .param p4, "g"    # I
    .param p5, "b"    # I
    .param p6, "txt"    # Ljava/lang/String;
    .param p7, "posX"    # F
    .param p8, "posY"    # F
    .param p9, "size"    # F

    .line 176
    iget-object v0, p0, Lcom/nkvt/DrawView;->mTextPaint:Landroid/graphics/Paint;

    invoke-virtual {v0}, Landroid/graphics/Paint;->clearShadowLayer()V

    .line 177
    iget-object v0, p0, Lcom/nkvt/DrawView;->mTextPaint:Landroid/graphics/Paint;

    invoke-static {p3, p4, p5}, Landroid/graphics/Color;->rgb(III)I

    move-result v1

    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setColor(I)V

    .line 178
    iget-object v0, p0, Lcom/nkvt/DrawView;->mTextPaint:Landroid/graphics/Paint;

    invoke-virtual {v0, p2}, Landroid/graphics/Paint;->setAlpha(I)V

    .line 179
    invoke-virtual {p0}, Lcom/nkvt/DrawView;->getRight()I

    move-result v0

    const/high16 v1, 0x41400000    # 12.0f

    const/16 v2, 0x780

    const/4 v3, 0x0

    if-gt v0, v2, :cond_3

    invoke-virtual {p0}, Lcom/nkvt/DrawView;->getBottom()I

    move-result v0

    if-le v0, v2, :cond_0

    goto :goto_1

    .line 183
    :cond_0
    invoke-virtual {p0}, Lcom/nkvt/DrawView;->getRight()I

    move-result v0

    if-eq v0, v2, :cond_2

    invoke-virtual {p0}, Lcom/nkvt/DrawView;->getBottom()I

    move-result v0

    if-ne v0, v2, :cond_1

    goto :goto_0

    .line 188
    :cond_1
    iget-object v0, p0, Lcom/nkvt/DrawView;->mTextPaint:Landroid/graphics/Paint;

    invoke-virtual {v0, p9}, Landroid/graphics/Paint;->setTextSize(F)V

    goto :goto_2

    .line 184
    :cond_2
    :goto_0
    iget-object v0, p0, Lcom/nkvt/DrawView;->mTextPaint:Landroid/graphics/Paint;

    const/high16 v2, 0x40000000    # 2.0f

    add-float/2addr v2, p9

    invoke-virtual {v0, v2}, Landroid/graphics/Paint;->setTextSize(F)V

    .line 185
    iget-object v0, p0, Lcom/nkvt/DrawView;->mTextPaint:Landroid/graphics/Paint;

    invoke-static {p3, p4, p5}, Landroid/graphics/Color;->rgb(III)I

    move-result v2

    invoke-virtual {v0, v1, v3, v3, v2}, Landroid/graphics/Paint;->setShadowLayer(FFFI)V

    goto :goto_2

    .line 180
    :cond_3
    :goto_1
    iget-object v0, p0, Lcom/nkvt/DrawView;->mTextPaint:Landroid/graphics/Paint;

    invoke-static {p3, p4, p5}, Landroid/graphics/Color;->rgb(III)I

    move-result v2

    invoke-virtual {v0, v1, v3, v3, v2}, Landroid/graphics/Paint;->setShadowLayer(FFFI)V

    .line 181
    iget-object v0, p0, Lcom/nkvt/DrawView;->mTextPaint:Landroid/graphics/Paint;

    const/high16 v2, 0x40800000    # 4.0f

    add-float/2addr v2, p9

    invoke-virtual {v0, v2}, Landroid/graphics/Paint;->setTextSize(F)V

    .line 189
    :goto_2
    iget-object v0, p0, Lcom/nkvt/DrawView;->mTextPaint:Landroid/graphics/Paint;

    invoke-static {p3, p4, p5}, Landroid/graphics/Color;->rgb(III)I

    move-result v2

    invoke-virtual {v0, v1, v3, v3, v2}, Landroid/graphics/Paint;->setShadowLayer(FFFI)V

    .line 190
    iget-object v0, p0, Lcom/nkvt/DrawView;->mTextPaint:Landroid/graphics/Paint;

    invoke-virtual {p1, p6, p7, p8, v0}, Landroid/graphics/Canvas;->drawText(Ljava/lang/String;FFLandroid/graphics/Paint;)V

    .line 191
    return-void
.end method

.method public DrawTextRect(Landroid/graphics/Canvas;IIIIIIIIIIIIFF)V
    .locals 18
    .param p1, "cvs"    # Landroid/graphics/Canvas;
    .param p2, "a"    # I
    .param p3, "r"    # I
    .param p4, "g"    # I
    .param p5, "b"    # I
    .param p6, "a1"    # I
    .param p7, "a2"    # I
    .param p8, "a3"    # I
    .param p9, "a4"    # I
    .param p10, "a5"    # I
    .param p11, "a6"    # I
    .param p12, "a7"    # I
    .param p13, "a8"    # I
    .param p14, "x"    # F
    .param p15, "y"    # F

    .line 225
    move-object/from16 v0, p0

    iget-object v1, v0, Lcom/nkvt/DrawView;->mRectPaint1:Landroid/graphics/Paint;

    invoke-static/range {p3 .. p5}, Landroid/graphics/Color;->rgb(III)I

    move-result v2

    invoke-virtual {v1, v2}, Landroid/graphics/Paint;->setColor(I)V

    .line 226
    iget-object v1, v0, Lcom/nkvt/DrawView;->mRectPaint1:Landroid/graphics/Paint;

    const v2, 0x3f8ccccd    # 1.1f

    invoke-virtual {v1, v2}, Landroid/graphics/Paint;->setStrokeWidth(F)V

    .line 227
    iget-object v1, v0, Lcom/nkvt/DrawView;->mRectPaint1:Landroid/graphics/Paint;

    move/from16 v3, p2

    invoke-virtual {v1, v3}, Landroid/graphics/Paint;->setAlpha(I)V

    .line 229
    iget-object v1, v0, Lcom/nkvt/DrawView;->mRectPaint2:Landroid/graphics/Paint;

    const/4 v4, 0x0

    const/16 v5, 0xff

    invoke-static {v4, v4, v5}, Landroid/graphics/Color;->rgb(III)I

    move-result v4

    invoke-virtual {v1, v4}, Landroid/graphics/Paint;->setColor(I)V

    .line 230
    iget-object v1, v0, Lcom/nkvt/DrawView;->mRectPaint2:Landroid/graphics/Paint;

    invoke-virtual {v1, v2}, Landroid/graphics/Paint;->setStrokeWidth(F)V

    .line 231
    iget-object v1, v0, Lcom/nkvt/DrawView;->mRectPaint2:Landroid/graphics/Paint;

    const/16 v2, 0x96

    invoke-virtual {v1, v2}, Landroid/graphics/Paint;->setAlpha(I)V

    .line 233
    move/from16 v1, p6

    int-to-float v2, v1

    sub-float v5, p14, v2

    move/from16 v2, p7

    int-to-float v4, v2

    sub-float v6, p15, v4

    move/from16 v10, p8

    int-to-float v4, v10

    add-float v7, p14, v4

    move/from16 v11, p9

    int-to-float v4, v11

    add-float v8, p15, v4

    iget-object v9, v0, Lcom/nkvt/DrawView;->mRectPaint1:Landroid/graphics/Paint;

    move-object/from16 v4, p1

    invoke-virtual/range {v4 .. v9}, Landroid/graphics/Canvas;->drawRect(FFFFLandroid/graphics/Paint;)V

    .line 234
    move/from16 v4, p10

    int-to-float v5, v4

    sub-float v13, p14, v5

    move/from16 v5, p11

    int-to-float v6, v5

    sub-float v14, p15, v6

    move/from16 v6, p12

    int-to-float v7, v6

    sub-float v15, p14, v7

    move/from16 v7, p13

    int-to-float v8, v7

    add-float v16, p15, v8

    iget-object v8, v0, Lcom/nkvt/DrawView;->mRectPaint2:Landroid/graphics/Paint;

    move-object/from16 v12, p1

    move-object/from16 v17, v8

    invoke-virtual/range {v12 .. v17}, Landroid/graphics/Canvas;->drawRect(FFFFLandroid/graphics/Paint;)V

    .line 235
    return-void
.end method

.method public InitializePaints()V
    .locals 3

    .line 79
    new-instance v0, Landroid/graphics/Paint;

    invoke-direct {v0}, Landroid/graphics/Paint;-><init>()V

    iput-object v0, p0, Lcom/nkvt/DrawView;->mStrokePaint:Landroid/graphics/Paint;

    .line 80
    sget-object v1, Landroid/graphics/Paint$Style;->STROKE:Landroid/graphics/Paint$Style;

    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setStyle(Landroid/graphics/Paint$Style;)V

    .line 81
    iget-object v0, p0, Lcom/nkvt/DrawView;->mStrokePaint:Landroid/graphics/Paint;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    .line 83
    new-instance v0, Landroid/graphics/Paint;

    invoke-direct {v0}, Landroid/graphics/Paint;-><init>()V

    iput-object v0, p0, Lcom/nkvt/DrawView;->mFilledPaint:Landroid/graphics/Paint;

    .line 84
    sget-object v2, Landroid/graphics/Paint$Style;->FILL:Landroid/graphics/Paint$Style;

    invoke-virtual {v0, v2}, Landroid/graphics/Paint;->setStyle(Landroid/graphics/Paint$Style;)V

    .line 85
    iget-object v0, p0, Lcom/nkvt/DrawView;->mFilledPaint:Landroid/graphics/Paint;

    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    .line 87
    new-instance v0, Landroid/graphics/Paint;

    invoke-direct {v0}, Landroid/graphics/Paint;-><init>()V

    iput-object v0, p0, Lcom/nkvt/DrawView;->mTextPaint:Landroid/graphics/Paint;

    .line 88
    sget-object v2, Landroid/graphics/Typeface;->MONOSPACE:Landroid/graphics/Typeface;

    invoke-virtual {v0, v2}, Landroid/graphics/Paint;->setTypeface(Landroid/graphics/Typeface;)Landroid/graphics/Typeface;

    .line 89
    iget-object v0, p0, Lcom/nkvt/DrawView;->mTextPaint:Landroid/graphics/Paint;

    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    .line 90
    iget-object v0, p0, Lcom/nkvt/DrawView;->mTextPaint:Landroid/graphics/Paint;

    sget-object v2, Landroid/graphics/Paint$Align;->CENTER:Landroid/graphics/Paint$Align;

    invoke-virtual {v0, v2}, Landroid/graphics/Paint;->setTextAlign(Landroid/graphics/Paint$Align;)V

    .line 92
    new-instance v0, Landroid/graphics/Paint;

    invoke-direct {v0}, Landroid/graphics/Paint;-><init>()V

    iput-object v0, p0, Lcom/nkvt/DrawView;->mRectPaint1:Landroid/graphics/Paint;

    .line 93
    sget-object v2, Landroid/graphics/Typeface;->MONOSPACE:Landroid/graphics/Typeface;

    invoke-virtual {v0, v2}, Landroid/graphics/Paint;->setTypeface(Landroid/graphics/Typeface;)Landroid/graphics/Typeface;

    .line 94
    iget-object v0, p0, Lcom/nkvt/DrawView;->mRectPaint1:Landroid/graphics/Paint;

    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    .line 95
    iget-object v0, p0, Lcom/nkvt/DrawView;->mRectPaint1:Landroid/graphics/Paint;

    sget-object v2, Landroid/graphics/Paint$Align;->CENTER:Landroid/graphics/Paint$Align;

    invoke-virtual {v0, v2}, Landroid/graphics/Paint;->setTextAlign(Landroid/graphics/Paint$Align;)V

    .line 97
    new-instance v0, Landroid/graphics/Paint;

    invoke-direct {v0}, Landroid/graphics/Paint;-><init>()V

    iput-object v0, p0, Lcom/nkvt/DrawView;->mRectPaint2:Landroid/graphics/Paint;

    .line 98
    sget-object v2, Landroid/graphics/Typeface;->MONOSPACE:Landroid/graphics/Typeface;

    invoke-virtual {v0, v2}, Landroid/graphics/Paint;->setTypeface(Landroid/graphics/Typeface;)Landroid/graphics/Typeface;

    .line 99
    iget-object v0, p0, Lcom/nkvt/DrawView;->mRectPaint2:Landroid/graphics/Paint;

    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    .line 100
    iget-object v0, p0, Lcom/nkvt/DrawView;->mRectPaint2:Landroid/graphics/Paint;

    sget-object v2, Landroid/graphics/Paint$Align;->CENTER:Landroid/graphics/Paint$Align;

    invoke-virtual {v0, v2}, Landroid/graphics/Paint;->setTextAlign(Landroid/graphics/Paint$Align;)V

    .line 102
    new-instance v0, Landroid/graphics/Paint;

    invoke-direct {v0}, Landroid/graphics/Paint;-><init>()V

    iput-object v0, p0, Lcom/nkvt/DrawView;->mBitMapPaint:Landroid/graphics/Paint;

    .line 103
    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    .line 104
    return-void
.end method

.method protected onDraw(Landroid/graphics/Canvas;)V
    .locals 3
    .param p1, "canvas"    # Landroid/graphics/Canvas;

    .line 50
    if-eqz p1, :cond_0

    invoke-virtual {p0}, Lcom/nkvt/DrawView;->getVisibility()I

    move-result v0

    if-nez v0, :cond_0

    .line 52
    invoke-virtual {p0, p1}, Lcom/nkvt/DrawView;->ClearCanvas(Landroid/graphics/Canvas;)V

    .line 53
    iget-object v0, p0, Lcom/nkvt/DrawView;->time:Ljava/util/Date;

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Ljava/util/Date;->setTime(J)V

    .line 54
    invoke-static {p0, p1}, Lcom/nkvt/Menu;->OnDrawLoad(Lcom/nkvt/DrawView;Landroid/graphics/Canvas;)V

    .line 56
    :cond_0
    return-void
.end method

.method public run()V
    .locals 8

    .line 60
    const/16 v0, 0xa

    invoke-static {v0}, Landroid/os/Process;->setThreadPriority(I)V

    .line 61
    :goto_0
    iget-object v0, p0, Lcom/nkvt/DrawView;->mThread:Ljava/lang/Thread;

    invoke-virtual {v0}, Ljava/lang/Thread;->isAlive()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/nkvt/DrawView;->mThread:Ljava/lang/Thread;

    invoke-virtual {v0}, Ljava/lang/Thread;->isInterrupted()Z

    move-result v0

    if-nez v0, :cond_0

    .line 65
    :try_start_0
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    .line 66
    .local v0, "t1":J
    invoke-virtual {p0}, Lcom/nkvt/DrawView;->postInvalidate()V

    .line 67
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v2

    sub-long/2addr v2, v0

    .line 68
    .local v2, "td":J
    const-wide/16 v4, 0x0

    iget-wide v6, p0, Lcom/nkvt/DrawView;->sleepTime:J

    sub-long/2addr v6, v2

    invoke-static {v4, v5, v6, v7}, Ljava/lang/Math;->min(JJ)J

    move-result-wide v4

    iget-wide v6, p0, Lcom/nkvt/DrawView;->sleepTime:J

    invoke-static {v4, v5, v6, v7}, Ljava/lang/Math;->max(JJ)J

    move-result-wide v4

    invoke-static {v4, v5}, Ljava/lang/Thread;->sleep(J)V
    :try_end_0
    .catch Ljava/lang/InterruptedException; {:try_start_0 .. :try_end_0} :catch_0

    .line 73
    .end local v0    # "t1":J
    .end local v2    # "td":J
    goto :goto_0

    .line 70
    :catch_0
    move-exception v0

    .line 72
    .local v0, "it":Ljava/lang/InterruptedException;
    return-void

    .line 75
    .end local v0    # "it":Ljava/lang/InterruptedException;
    :cond_0
    return-void
.end method
