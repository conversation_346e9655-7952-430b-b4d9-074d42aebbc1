.class public final synthetic Lcom/nkvt/-$$Lambda$Login$0W2MsUS3jkOqTZbWxqtBXup1__g;
.super Ljava/lang/Object;
.source "lambda"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic f$0:Lcom/nkvt/Login;

.field public final synthetic f$1:Ljava/lang/String;


# direct methods
.method public synthetic constructor <init>(Lcom/nkvt/Login;Ljava/lang/String;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/nkvt/-$$Lambda$Login$0W2MsUS3jkOqTZbWxqtBXup1__g;->f$0:Lcom/nkvt/Login;

    iput-object p2, p0, Lcom/nkvt/-$$Lambda$Login$0W2MsUS3jkOqTZbWxqtBXup1__g;->f$1:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    iget-object v0, p0, Lcom/nkvt/-$$Lambda$Login$0W2MsUS3jkOqTZbWxqtBXup1__g;->f$0:Lcom/nkvt/Login;

    iget-object v1, p0, Lcom/nkvt/-$$Lambda$Login$0W2MsUS3jkOqTZbWxqtBXup1__g;->f$1:Ljava/lang/String;

    invoke-virtual {v0, v1}, Lcom/nkvt/Login;->lambda$showToast$5$Login(Ljava/lang/String;)V

    return-void
.end method
