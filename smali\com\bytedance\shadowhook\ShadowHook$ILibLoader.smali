.class public interface abstract Lcom/bytedance/shadowhook/ShadowHook$ILibLoader;
.super Ljava/lang/Object;
.source "ShadowHook.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bytedance/shadowhook/ShadowHook;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "ILibLoader"
.end annotation


# virtual methods
.method public abstract loadLibrary(Ljava/lang/String;)V
.end method
