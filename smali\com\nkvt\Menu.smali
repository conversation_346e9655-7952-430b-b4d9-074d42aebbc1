.class public Lcom/nkvt/Menu;
.super Ljava/lang/Object;
.source "Menu.java"


# static fields
.field public static PrimaryColor:I

.field static container_features:Landroid/widget/LinearLayout;

.field private static context:Landroid/content/Context;

.field private static utils:Lcom/nkvt/Utils;


# instance fields
.field private buttonClick:I

.field drawView:Lcom/nkvt/DrawView;

.field private frameLayout:Landroid/widget/FrameLayout;

.field private injectType:I

.field private target:Ljava/lang/String;

.field private windowManager:Landroid/view/WindowManager;

.field windowManagerDrawViewParams:Landroid/view/WindowManager$LayoutParams;

.field private windowManagerParams:Landroid/view/WindowManager$LayoutParams;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 49
    const/high16 v0, -0x10000

    sput v0, Lcom/nkvt/Menu;->PrimaryColor:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;I)V
    .locals 1
    .param p1, "globContext"    # Landroid/content/Context;
    .param p2, "glob_injectType"    # I

    .line 89
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 44
    const-string v0, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->diGfPʻᵢʿᴵﹳˎʼˋᵔˉᐧˋˈʿיʻᵢoRjFj()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/nkvt/Menu;->target:Ljava/lang/String;

    .line 48
    const/4 v0, 0x0

    iput v0, p0, Lcom/nkvt/Menu;->buttonClick:I

    .line 90
    sput-object p1, Lcom/nkvt/Menu;->context:Landroid/content/Context;

    .line 91
    new-instance v0, Lcom/nkvt/Utils;

    invoke-direct {v0, p1}, Lcom/nkvt/Utils;-><init>(Landroid/content/Context;)V

    sput-object v0, Lcom/nkvt/Menu;->utils:Lcom/nkvt/Utils;

    .line 92
    iput p2, p0, Lcom/nkvt/Menu;->injectType:I

    .line 93
    const-string v0, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->xJigAיᴵˆﹳˊʿⁱˆⁱˑˏﹳʿʻvtOOF()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Ljava/lang/System;->loadLibrary(Ljava/lang/String;)V

    .line 94
    invoke-virtual {p0}, Lcom/nkvt/Menu;->onCreate()V

    .line 95
    return-void
.end method

.method public static native ChangesID(II)V
.end method

.method public static native Functions()V
.end method

.method public static native Init()V
.end method

.method private InjectX86(Ljava/lang/String;)Z
    .locals 14
    .param p1, "Lib"    # Ljava/lang/String;

    .line 483
    const-string v0, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->HHeGLˋʿـﾞﹳᴵᵢיﹶˉʿᐧˉˏٴﾞיʽQbAdM()Ljava/lang/String;

    move-result-object v0

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->IywOFˉˋʽᵢᐧˎʻᵢˋﹶTEORZ()Ljava/lang/String;

    move-result-object v1

    const-string v2, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->RgJFLﾞˆˎˆᵢʼـˊﹳٴᐧʽـˊٴˋxJAAk()Ljava/lang/String;

    move-result-object v2

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->UWpDNʻᵎˆˑﹳⁱˎˉˏʾˋˏʽʾﾞʽᵢיٴﾞLTiMN()Ljava/lang/String;

    move-result-object v3

    const/4 v4, 0x0

    :try_start_0
    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    sget-object v6, Lcom/nkvt/Menu;->context:Landroid/content/Context;

    invoke-virtual {v6}, Landroid/content/Context;->getApplicationInfo()Landroid/content/pm/ApplicationInfo;

    move-result-object v6

    iget-object v6, v6, Landroid/content/pm/ApplicationInfo;->nativeLibraryDir:Ljava/lang/String;

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v6, Ljava/io/File;->separator:Ljava/lang/String;

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v6, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->LOlKzˉיˋˋٴˏٴᵔﾞˑﹶˏˊˋʼˉgVyOQ()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    .line 484
    .local v5, "injector":Ljava/lang/String;
    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    sget-object v7, Lcom/nkvt/Menu;->context:Landroid/content/Context;

    invoke-virtual {v7}, Landroid/content/Context;->getApplicationInfo()Landroid/content/pm/ApplicationInfo;

    move-result-object v7

    iget-object v7, v7, Landroid/content/pm/ApplicationInfo;->nativeLibraryDir:Ljava/lang/String;

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v7, Ljava/io/File;->separator:Ljava/lang/String;

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v6

    .line 486
    .local v6, "payload_source":Ljava/lang/String;
    const-string v7, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->tHzgyﾞـˋˋᵔـᐧˈﾞﾞᵢᵢﾞʼjLkwO()Ljava/lang/String;

    move-result-object v7

    .line 487
    .local v7, "injector_dest":Ljava/lang/String;
    new-instance v8, Ljava/lang/StringBuilder;

    invoke-direct {v8}, Ljava/lang/StringBuilder;-><init>()V

    const-string v9, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->tcGHeˆˋᵔᴵᴵᵔᵢיᴵˏˋʽⁱﾞﾞٴˊʾBnqki()Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v8, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v8, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v8}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v8

    .line 488
    .local v8, "payload_dest":Ljava/lang/String;
    const-string v9, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->kPAuXᴵʿˏˊﹶˋᵎʽʽיﹳˈuxPsh()Ljava/lang/String;

    move-result-object v9

    .line 491
    .local v9, "payload_alias":Ljava/lang/String;
    const/4 v10, 0x1

    new-array v11, v10, [Ljava/lang/String;

    new-instance v12, Ljava/lang/StringBuilder;

    invoke-direct {v12}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v12, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v12

    aput-object v12, v11, v4

    invoke-static {v11}, Lcom/topjohnwu/superuser/Shell;->su([Ljava/lang/String;)Lcom/topjohnwu/superuser/Shell$Job;

    move-result-object v11

    invoke-virtual {v11}, Lcom/topjohnwu/superuser/Shell$Job;->exec()Lcom/topjohnwu/superuser/Shell$Result;

    .line 492
    new-array v11, v10, [Ljava/lang/String;

    new-instance v12, Ljava/lang/StringBuilder;

    invoke-direct {v12}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v12, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v12

    aput-object v12, v11, v4

    invoke-static {v11}, Lcom/topjohnwu/superuser/Shell;->su([Ljava/lang/String;)Lcom/topjohnwu/superuser/Shell$Job;

    move-result-object v11

    invoke-virtual {v11}, Lcom/topjohnwu/superuser/Shell$Job;->exec()Lcom/topjohnwu/superuser/Shell$Result;

    .line 493
    new-array v11, v10, [Ljava/lang/String;

    new-instance v12, Ljava/lang/StringBuilder;

    invoke-direct {v12}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v12, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v12

    aput-object v12, v11, v4

    invoke-static {v11}, Lcom/topjohnwu/superuser/Shell;->su([Ljava/lang/String;)Lcom/topjohnwu/superuser/Shell$Job;

    move-result-object v11

    invoke-virtual {v11}, Lcom/topjohnwu/superuser/Shell$Job;->exec()Lcom/topjohnwu/superuser/Shell$Result;

    .line 496
    new-array v11, v10, [Ljava/lang/String;

    new-instance v12, Ljava/lang/StringBuilder;

    invoke-direct {v12}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v12, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v12

    aput-object v12, v11, v4

    invoke-static {v11}, Lcom/topjohnwu/superuser/Shell;->su([Ljava/lang/String;)Lcom/topjohnwu/superuser/Shell$Job;

    move-result-object v11

    invoke-virtual {v11}, Lcom/topjohnwu/superuser/Shell$Job;->exec()Lcom/topjohnwu/superuser/Shell$Result;

    move-result-object v11

    .line 500
    .local v11, "result":Lcom/topjohnwu/superuser/Shell$Result;
    new-array v12, v10, [Ljava/lang/String;

    new-instance v13, Ljava/lang/StringBuilder;

    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v13, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v13

    aput-object v13, v12, v4

    invoke-static {v12}, Lcom/topjohnwu/superuser/Shell;->su([Ljava/lang/String;)Lcom/topjohnwu/superuser/Shell$Job;

    move-result-object v12

    invoke-virtual {v12}, Lcom/topjohnwu/superuser/Shell$Job;->exec()Lcom/topjohnwu/superuser/Shell$Result;

    .line 501
    new-array v12, v10, [Ljava/lang/String;

    new-instance v13, Ljava/lang/StringBuilder;

    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v13, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    aput-object v1, v12, v4

    invoke-static {v12}, Lcom/topjohnwu/superuser/Shell;->su([Ljava/lang/String;)Lcom/topjohnwu/superuser/Shell$Job;

    move-result-object v1

    invoke-virtual {v1}, Lcom/topjohnwu/superuser/Shell$Job;->exec()Lcom/topjohnwu/superuser/Shell$Result;

    .line 504
    new-array v1, v10, [Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    aput-object v2, v1, v4

    invoke-static {v1}, Lcom/topjohnwu/superuser/Shell;->su([Ljava/lang/String;)Lcom/topjohnwu/superuser/Shell$Job;

    move-result-object v1

    invoke-virtual {v1}, Lcom/topjohnwu/superuser/Shell$Job;->exec()Lcom/topjohnwu/superuser/Shell$Result;

    .line 505
    new-array v1, v10, [Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    aput-object v2, v1, v4

    invoke-static {v1}, Lcom/topjohnwu/superuser/Shell;->su([Ljava/lang/String;)Lcom/topjohnwu/superuser/Shell$Job;

    move-result-object v1

    invoke-virtual {v1}, Lcom/topjohnwu/superuser/Shell$Job;->exec()Lcom/topjohnwu/superuser/Shell$Result;

    .line 506
    new-array v1, v10, [Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    aput-object v0, v1, v4

    invoke-static {v1}, Lcom/topjohnwu/superuser/Shell;->su([Ljava/lang/String;)Lcom/topjohnwu/superuser/Shell$Job;

    move-result-object v0

    invoke-virtual {v0}, Lcom/topjohnwu/superuser/Shell$Job;->exec()Lcom/topjohnwu/superuser/Shell$Result;

    .line 509
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->FQKBKﾞـʽˋˆᵎˉˑᐧﹳˈﾞˊʿyIEZO()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->JKBeFˆﾞˈˎⁱᵢˎʾᵢKfNgU()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 511
    .local v0, "injectCmd":Ljava/lang/String;
    new-array v1, v10, [Ljava/lang/String;

    aput-object v0, v1, v4

    invoke-static {v1}, Lcom/topjohnwu/superuser/Shell;->su([Ljava/lang/String;)Lcom/topjohnwu/superuser/Shell$Job;

    move-result-object v1

    invoke-virtual {v1}, Lcom/topjohnwu/superuser/Shell$Job;->exec()Lcom/topjohnwu/superuser/Shell$Result;

    move-result-object v1

    .line 514
    .end local v11    # "result":Lcom/topjohnwu/superuser/Shell$Result;
    .local v1, "result":Lcom/topjohnwu/superuser/Shell$Result;
    new-array v2, v10, [Ljava/lang/String;

    new-instance v11, Ljava/lang/StringBuilder;

    invoke-direct {v11}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v11, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v11

    aput-object v11, v2, v4

    invoke-static {v2}, Lcom/topjohnwu/superuser/Shell;->su([Ljava/lang/String;)Lcom/topjohnwu/superuser/Shell$Job;

    move-result-object v2

    invoke-virtual {v2}, Lcom/topjohnwu/superuser/Shell$Job;->exec()Lcom/topjohnwu/superuser/Shell$Result;

    .line 515
    new-array v2, v10, [Ljava/lang/String;

    new-instance v11, Ljava/lang/StringBuilder;

    invoke-direct {v11}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v11, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v11

    aput-object v11, v2, v4

    invoke-static {v2}, Lcom/topjohnwu/superuser/Shell;->su([Ljava/lang/String;)Lcom/topjohnwu/superuser/Shell$Job;

    move-result-object v2

    invoke-virtual {v2}, Lcom/topjohnwu/superuser/Shell$Job;->exec()Lcom/topjohnwu/superuser/Shell$Result;

    .line 516
    new-array v2, v10, [Ljava/lang/String;

    new-instance v11, Ljava/lang/StringBuilder;

    invoke-direct {v11}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v11, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v2, v4

    invoke-static {v2}, Lcom/topjohnwu/superuser/Shell;->su([Ljava/lang/String;)Lcom/topjohnwu/superuser/Shell$Job;

    move-result-object v2

    invoke-virtual {v2}, Lcom/topjohnwu/superuser/Shell$Job;->exec()Lcom/topjohnwu/superuser/Shell$Result;

    .line 518
    invoke-static {}, Lcom/nkvt/Menu;->Functions()V

    .line 519
    sget-object v2, Lcom/nkvt/Menu;->context:Landroid/content/Context;

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->LNbdUʾיʾﹳٴˆﹶʼᐧʾـᐧˆᵢjpXHM()Ljava/lang/String;

    move-result-object v3

    invoke-static {v2, v3, v4}, Landroid/widget/Toast;->makeText(Landroid/content/Context;Ljava/lang/CharSequence;I)Landroid/widget/Toast;

    move-result-object v2

    invoke-virtual {v2}, Landroid/widget/Toast;->show()V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 520
    return v10

    .line 521
    .end local v0    # "injectCmd":Ljava/lang/String;
    .end local v1    # "result":Lcom/topjohnwu/superuser/Shell$Result;
    .end local v5    # "injector":Ljava/lang/String;
    .end local v6    # "payload_source":Ljava/lang/String;
    .end local v7    # "injector_dest":Ljava/lang/String;
    .end local v8    # "payload_dest":Ljava/lang/String;
    .end local v9    # "payload_alias":Ljava/lang/String;
    :catch_0
    move-exception v0

    .line 523
    .local v0, "e":Ljava/lang/Exception;
    invoke-virtual {v0}, Ljava/lang/Exception;->printStackTrace()V

    .line 524
    return v4
.end method

.method public static native OnDrawLoad(Lcom/nkvt/DrawView;Landroid/graphics/Canvas;)V
.end method

.method static synthetic access$000(Lcom/nkvt/Menu;)I
    .locals 1
    .param p0, "x0"    # Lcom/nkvt/Menu;

    .line 35
    iget v0, p0, Lcom/nkvt/Menu;->buttonClick:I

    return v0
.end method

.method static synthetic access$008(Lcom/nkvt/Menu;)I
    .locals 2
    .param p0, "x0"    # Lcom/nkvt/Menu;

    .line 35
    iget v0, p0, Lcom/nkvt/Menu;->buttonClick:I

    add-int/lit8 v1, v0, 0x1

    iput v1, p0, Lcom/nkvt/Menu;->buttonClick:I

    return v0
.end method

.method static synthetic access$100(Lcom/nkvt/Menu;)I
    .locals 1
    .param p0, "x0"    # Lcom/nkvt/Menu;

    .line 35
    iget v0, p0, Lcom/nkvt/Menu;->injectType:I

    return v0
.end method

.method static synthetic access$200(Lcom/nkvt/Menu;Ljava/lang/String;)Z
    .locals 1
    .param p0, "x0"    # Lcom/nkvt/Menu;
    .param p1, "x1"    # Ljava/lang/String;

    .line 35
    invoke-direct {p0, p1}, Lcom/nkvt/Menu;->InjectX86(Ljava/lang/String;)Z

    move-result v0

    return v0
.end method

.method static synthetic access$300(Lcom/nkvt/Menu;)Landroid/widget/FrameLayout;
    .locals 1
    .param p0, "x0"    # Lcom/nkvt/Menu;

    .line 35
    iget-object v0, p0, Lcom/nkvt/Menu;->frameLayout:Landroid/widget/FrameLayout;

    return-object v0
.end method

.method static synthetic access$400(Lcom/nkvt/Menu;)Landroid/view/WindowManager$LayoutParams;
    .locals 1
    .param p0, "x0"    # Lcom/nkvt/Menu;

    .line 35
    iget-object v0, p0, Lcom/nkvt/Menu;->windowManagerParams:Landroid/view/WindowManager$LayoutParams;

    return-object v0
.end method

.method static synthetic access$500(Lcom/nkvt/Menu;)Landroid/view/WindowManager;
    .locals 1
    .param p0, "x0"    # Lcom/nkvt/Menu;

    .line 35
    iget-object v0, p0, Lcom/nkvt/Menu;->windowManager:Landroid/view/WindowManager;

    return-object v0
.end method

.method public static addCategory(Ljava/lang/String;)V
    .locals 5
    .param p0, "name"    # Ljava/lang/String;

    .line 290
    new-instance v0, Landroid/graphics/drawable/GradientDrawable;

    invoke-direct {v0}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    .line 291
    .local v0, "gradientDrawable":Landroid/graphics/drawable/GradientDrawable;
    sget v1, Lcom/nkvt/Menu;->PrimaryColor:I

    invoke-virtual {v0, v1}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 292
    sget-object v1, Lcom/nkvt/Menu;->utils:Lcom/nkvt/Utils;

    const/4 v2, 0x4

    invoke-virtual {v1, v2}, Lcom/nkvt/Utils;->FixDP(I)I

    move-result v1

    int-to-float v1, v1

    invoke-virtual {v0, v1}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 294
    new-instance v1, Landroid/widget/LinearLayout;

    sget-object v2, Lcom/nkvt/Menu;->context:Landroid/content/Context;

    invoke-direct {v1, v2}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    .line 295
    .local v1, "linearLayout":Landroid/widget/LinearLayout;
    new-instance v2, Landroid/widget/LinearLayout$LayoutParams;

    sget-object v3, Lcom/nkvt/Menu;->utils:Lcom/nkvt/Utils;

    const/16 v4, 0x19

    invoke-virtual {v3, v4}, Lcom/nkvt/Utils;->FixDP(I)I

    move-result v3

    const/4 v4, -0x1

    invoke-direct {v2, v4, v3}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v1, v2}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 296
    invoke-virtual {v1, v0}, Landroid/widget/LinearLayout;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 297
    const/16 v2, 0x11

    invoke-virtual {v1, v2}, Landroid/widget/LinearLayout;->setGravity(I)V

    .line 299
    new-instance v2, Landroid/widget/TextView;

    sget-object v3, Lcom/nkvt/Menu;->context:Landroid/content/Context;

    invoke-direct {v2, v3}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    .line 300
    .local v2, "textView":Landroid/widget/TextView;
    invoke-virtual {v2, p0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 301
    const/high16 v3, 0x41300000    # 11.0f

    invoke-virtual {v2, v3}, Landroid/widget/TextView;->setTextSize(F)V

    .line 302
    invoke-virtual {v2, v4}, Landroid/widget/TextView;->setTextColor(I)V

    .line 304
    sget-object v3, Lcom/nkvt/Menu;->container_features:Landroid/widget/LinearLayout;

    invoke-virtual {v3, v1}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 305
    invoke-virtual {v1, v2}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 306
    return-void
.end method

.method public static addSeekBar(Ljava/lang/String;IILjava/lang/String;I)V
    .locals 11
    .param p0, "name"    # Ljava/lang/String;
    .param p1, "value"    # I
    .param p2, "max"    # I
    .param p3, "type"    # Ljava/lang/String;
    .param p4, "ID"    # I

    .line 349
    new-instance v0, Landroid/widget/LinearLayout;

    sget-object v1, Lcom/nkvt/Menu;->context:Landroid/content/Context;

    invoke-direct {v0, v1}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    .line 350
    .local v0, "linearLayout":Landroid/widget/LinearLayout;
    new-instance v1, Landroid/widget/LinearLayout$LayoutParams;

    const/4 v2, -0x1

    const/4 v3, -0x2

    invoke-direct {v1, v2, v3}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 351
    sget-object v1, Lcom/nkvt/Menu;->utils:Lcom/nkvt/Utils;

    const/4 v3, 0x2

    invoke-virtual {v1, v3}, Lcom/nkvt/Utils;->FixDP(I)I

    move-result v1

    sget-object v4, Lcom/nkvt/Menu;->utils:Lcom/nkvt/Utils;

    invoke-virtual {v4, v3}, Lcom/nkvt/Utils;->FixDP(I)I

    move-result v4

    const/4 v5, 0x0

    invoke-virtual {v0, v5, v1, v5, v4}, Landroid/widget/LinearLayout;->setPadding(IIII)V

    .line 352
    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 354
    new-instance v4, Landroid/widget/TextView;

    sget-object v5, Lcom/nkvt/Menu;->context:Landroid/content/Context;

    invoke-direct {v4, v5}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    .line 355
    .local v4, "textView":Landroid/widget/TextView;
    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->TbLmOˎʼﹳʿـᵢˋﹶﹶˆﹶᵔᵔˉˆᵔᵢᵔySPqB()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {p0, v6}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v5, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v4, v5}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 356
    const/high16 v5, 0x41300000    # 11.0f

    invoke-virtual {v4, v5}, Landroid/widget/TextView;->setTextSize(F)V

    .line 357
    invoke-virtual {v4, v2}, Landroid/widget/TextView;->setTextColor(I)V

    .line 358
    const-string v2, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->oidSKˊᴵˎᵔיᐧʿˊיˑˈᵔˏˏˈˎⁱᵢjqbCs()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p3, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v5

    const/16 v6, 0x9

    const-string v7, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->XYtDaʻʾʾˎˏˑˈٴـᴵˎﹶᵔˑˈˎʼˏﹳᐧoiZQf()Ljava/lang/String;

    move-result-object v7

    const-string v8, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->noPgxʽᵎˏﹳʼᵔʼᐧʿˆʻʼʾˏـᵎsSSNo()Ljava/lang/String;

    move-result-object v8

    if-eqz v5, :cond_9

    .line 359
    if-nez p1, :cond_0

    .line 360
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v5, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->XVTmQˏٴﹶᵔˈﹶˉˉRThgu()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v1, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0

    .line 361
    :cond_0
    if-ne p1, v1, :cond_1

    .line 362
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v5, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->nuKbmᴵⁱיˊʻᐧᵢᴵᵎˋivMnQ()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v1, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0

    .line 363
    :cond_1
    if-ne p1, v3, :cond_2

    .line 364
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v5, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->jHeesᐧﹳʼᵔᐧʻᵎﹶﹳˊⁱʾⁱˈˆʼˏˊﾞᵔHiExW()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v1, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0

    .line 365
    :cond_2
    const/4 v1, 0x3

    if-ne p1, v1, :cond_3

    .line 366
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v5, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->ndexPˋﹳˑˋˏﹶˎʼˏᵢʾˑˈⁱʿᴵﾞʿˆᴵⁱٴmYaMy()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v1, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0

    .line 367
    :cond_3
    const/4 v1, 0x4

    if-ne p1, v1, :cond_4

    .line 368
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v5, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->OqHzaᴵᵢᐧـˏᐧـˏﹶﾞᵔᵎˆⁱᵢʼʿˈMxeqt()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v1, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0

    .line 369
    :cond_4
    const/4 v1, 0x5

    if-ne p1, v1, :cond_5

    .line 370
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v5, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->FOqUQˉᵎʾˉˏʽˏﹶٴˏٴᵢٴʼⁱᵎﾞᐧˆⁱBpZak()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v1, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0

    .line 371
    :cond_5
    const/4 v1, 0x6

    if-ne p1, v1, :cond_6

    .line 372
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v5, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->JafpLᵢˋⁱˏᵎˏʾˈⁱᵔʼـˎﹶRhUvr()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v1, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0

    .line 373
    :cond_6
    const/4 v1, 0x7

    if-ne p1, v1, :cond_7

    .line 374
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v5, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->gijYkٴˊᵢʻיʼˈﾞﾞᵎﹶٴʻʽEYeyH()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v1, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0

    .line 375
    :cond_7
    const/16 v1, 0x8

    if-ne p1, v1, :cond_8

    .line 376
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v5, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->yzsJHˆיٴʿⁱˆᵢייﾞˈᵎʾˆᐧʾʾᵢQuqSt()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v1, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto/16 :goto_0

    .line 377
    :cond_8
    if-ne p1, v6, :cond_f

    .line 378
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v5, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->LWpsXᵎʼˈʼˈיʽʾᵢᵎﹶיˉʼʾᵔʻٴˆˊRmoes()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v1, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto :goto_0

    .line 380
    :cond_9
    invoke-virtual {p3, v8}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_c

    .line 381
    if-nez p1, :cond_a

    .line 382
    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->KjzFYˎᵔיᵢˆᵔٴˏˉˎـˆeJUsx()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto :goto_0

    .line 383
    :cond_a
    if-ne p1, v1, :cond_b

    .line 384
    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->UMFyWˈٴˋˎٴˆﹶʿˎﾞـʻˋˋـʽʿٴvZRHe()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto :goto_0

    .line 385
    :cond_b
    if-ne p1, v3, :cond_f

    .line 386
    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->ofCYhיʻﹶʽـﹳᵢˆʼˎⁱʼﹳʻxVfxC()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto :goto_0

    .line 388
    :cond_c
    invoke-virtual {p3, v7}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_f

    .line 389
    if-nez p1, :cond_d

    .line 390
    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->FyZhOˑᐧˑﹶﾞʿˑٴˎـPovbW()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto :goto_0

    .line 391
    :cond_d
    if-ne p1, v1, :cond_e

    .line 392
    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->SKOofـⁱˎᵔᵎʼᴵﾞPaZgU()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    goto :goto_0

    .line 393
    :cond_e
    if-ne p1, v3, :cond_f

    .line 394
    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->ZohCnˈˈـˉˑٴʼﾞᵔﹳـˈˈᐧSJFFz()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 398
    :cond_f
    :goto_0
    new-instance v1, Landroid/widget/SeekBar;

    sget-object v5, Lcom/nkvt/Menu;->context:Landroid/content/Context;

    invoke-direct {v1, v5}, Landroid/widget/SeekBar;-><init>(Landroid/content/Context;)V

    .line 399
    .local v1, "seekBar":Landroid/widget/SeekBar;
    invoke-virtual {v1}, Landroid/widget/SeekBar;->getThumb()Landroid/graphics/drawable/Drawable;

    move-result-object v5

    sget v9, Lcom/nkvt/Menu;->PrimaryColor:I

    sget-object v10, Landroid/graphics/PorterDuff$Mode;->SRC_IN:Landroid/graphics/PorterDuff$Mode;

    invoke-virtual {v5, v9, v10}, Landroid/graphics/drawable/Drawable;->setColorFilter(ILandroid/graphics/PorterDuff$Mode;)V

    .line 400
    invoke-virtual {v1}, Landroid/widget/SeekBar;->getProgressDrawable()Landroid/graphics/drawable/Drawable;

    move-result-object v5

    sget v9, Lcom/nkvt/Menu;->PrimaryColor:I

    sget-object v10, Landroid/graphics/PorterDuff$Mode;->SRC_IN:Landroid/graphics/PorterDuff$Mode;

    invoke-virtual {v5, v9, v10}, Landroid/graphics/drawable/Drawable;->setColorFilter(ILandroid/graphics/PorterDuff$Mode;)V

    .line 401
    invoke-virtual {v1, p1}, Landroid/widget/SeekBar;->setProgress(I)V

    .line 402
    invoke-virtual {v1, p2}, Landroid/widget/SeekBar;->setMax(I)V

    .line 403
    invoke-virtual {p3, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_10

    .line 404
    invoke-virtual {v1, v6}, Landroid/widget/SeekBar;->setMax(I)V

    goto :goto_1

    .line 405
    :cond_10
    invoke-virtual {p3, v8}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_11

    .line 406
    invoke-virtual {v1, v3}, Landroid/widget/SeekBar;->setMax(I)V

    goto :goto_1

    .line 407
    :cond_11
    invoke-virtual {p3, v7}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_12

    .line 408
    invoke-virtual {v1, v3}, Landroid/widget/SeekBar;->setMax(I)V

    .line 411
    :cond_12
    :goto_1
    new-instance v2, Lcom/nkvt/Menu$6;

    invoke-direct {v2, p3, v4, p0, p4}, Lcom/nkvt/Menu$6;-><init>(Ljava/lang/String;Landroid/widget/TextView;Ljava/lang/String;I)V

    invoke-virtual {v1, v2}, Landroid/widget/SeekBar;->setOnSeekBarChangeListener(Landroid/widget/SeekBar$OnSeekBarChangeListener;)V

    .line 471
    sget-object v2, Lcom/nkvt/Menu;->container_features:Landroid/widget/LinearLayout;

    invoke-virtual {v2, v0}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 472
    invoke-virtual {v0, v4}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 473
    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 474
    return-void
.end method

.method public static addSwitch(Ljava/lang/String;I)V
    .locals 8
    .param p0, "name"    # Ljava/lang/String;
    .param p1, "ID"    # I

    .line 309
    new-instance v0, Landroid/widget/LinearLayout;

    sget-object v1, Lcom/nkvt/Menu;->context:Landroid/content/Context;

    invoke-direct {v0, v1}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    .line 310
    .local v0, "linearLayout":Landroid/widget/LinearLayout;
    new-instance v1, Landroid/widget/LinearLayout$LayoutParams;

    const/4 v2, -0x1

    const/4 v3, -0x2

    invoke-direct {v1, v2, v3}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 311
    sget-object v1, Lcom/nkvt/Menu;->utils:Lcom/nkvt/Utils;

    const/4 v4, 0x5

    invoke-virtual {v1, v4}, Lcom/nkvt/Utils;->FixDP(I)I

    move-result v1

    sget-object v5, Lcom/nkvt/Menu;->utils:Lcom/nkvt/Utils;

    const/4 v6, 0x2

    invoke-virtual {v5, v6}, Lcom/nkvt/Utils;->FixDP(I)I

    move-result v5

    sget-object v7, Lcom/nkvt/Menu;->utils:Lcom/nkvt/Utils;

    invoke-virtual {v7, v4}, Lcom/nkvt/Utils;->FixDP(I)I

    move-result v4

    sget-object v7, Lcom/nkvt/Menu;->utils:Lcom/nkvt/Utils;

    invoke-virtual {v7, v6}, Lcom/nkvt/Utils;->FixDP(I)I

    move-result v6

    invoke-virtual {v0, v1, v5, v4, v6}, Landroid/widget/LinearLayout;->setPadding(IIII)V

    .line 312
    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 313
    const/16 v1, 0x10

    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->setGravity(I)V

    .line 315
    new-instance v4, Landroid/widget/TextView;

    sget-object v5, Lcom/nkvt/Menu;->context:Landroid/content/Context;

    invoke-direct {v4, v5}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    .line 316
    .local v4, "textView":Landroid/widget/TextView;
    new-instance v5, Landroid/widget/LinearLayout$LayoutParams;

    const/high16 v6, 0x3f800000    # 1.0f

    invoke-direct {v5, v2, v3, v6}, Landroid/widget/LinearLayout$LayoutParams;-><init>(IIF)V

    invoke-virtual {v4, v5}, Landroid/widget/TextView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 317
    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setGravity(I)V

    .line 318
    invoke-virtual {v4, p0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 319
    const v1, -0x777778

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setTextColor(I)V

    .line 320
    const/high16 v1, 0x41400000    # 12.0f

    invoke-virtual {v4, v1}, Landroid/widget/TextView;->setTextSize(F)V

    .line 322
    new-instance v1, Landroid/widget/CheckBox;

    sget-object v2, Lcom/nkvt/Menu;->context:Landroid/content/Context;

    invoke-direct {v1, v2}, Landroid/widget/CheckBox;-><init>(Landroid/content/Context;)V

    .line 323
    .local v1, "checkBox":Landroid/widget/CheckBox;
    new-instance v2, Landroid/widget/LinearLayout$LayoutParams;

    invoke-direct {v2, v3, v3}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v1, v2}, Landroid/widget/CheckBox;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 324
    invoke-virtual {v1}, Landroid/widget/CheckBox;->getButtonDrawable()Landroid/graphics/drawable/Drawable;

    move-result-object v2

    const/high16 v3, -0x10000

    invoke-virtual {v2, v3}, Landroid/graphics/drawable/Drawable;->setTint(I)V

    .line 325
    invoke-virtual {v1, p0}, Landroid/widget/CheckBox;->setContentDescription(Ljava/lang/CharSequence;)V

    .line 327
    new-instance v2, Lcom/nkvt/Menu$4;

    invoke-direct {v2, p1, v4}, Lcom/nkvt/Menu$4;-><init>(ILandroid/widget/TextView;)V

    invoke-virtual {v1, v2}, Landroid/widget/CheckBox;->setOnCheckedChangeListener(Landroid/widget/CompoundButton$OnCheckedChangeListener;)V

    .line 335
    new-instance v2, Lcom/nkvt/Menu$5;

    invoke-direct {v2, v1}, Lcom/nkvt/Menu$5;-><init>(Landroid/widget/CheckBox;)V

    invoke-virtual {v0, v2}, Landroid/widget/LinearLayout;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 342
    invoke-virtual {v0, v4}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 343
    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 344
    sget-object v2, Lcom/nkvt/Menu;->container_features:Landroid/widget/LinearLayout;

    invoke-virtual {v2, v0}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 345
    return-void
.end method

.method private native imageBase64()Ljava/lang/String;
.end method

.method private onTouchListener()Landroid/view/View$OnTouchListener;
    .locals 1

    .line 251
    new-instance v0, Lcom/nkvt/Menu$3;

    invoke-direct {v0, p0}, Lcom/nkvt/Menu$3;-><init>(Lcom/nkvt/Menu;)V

    return-object v0
.end method


# virtual methods
.method public DrawCanvas()V
    .locals 8

    .line 74
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x1a

    if-lt v0, v1, :cond_0

    .line 75
    const/16 v0, 0x7f6

    .local v0, "LAYOUT_FLAG":I
    goto :goto_0

    .line 77
    .end local v0    # "LAYOUT_FLAG":I
    :cond_0
    const/16 v0, 0x7d2

    .line 80
    .restart local v0    # "LAYOUT_FLAG":I
    :goto_0
    new-instance v1, Lcom/nkvt/DrawView;

    sget-object v2, Lcom/nkvt/Menu;->context:Landroid/content/Context;

    invoke-direct {v1, v2}, Lcom/nkvt/DrawView;-><init>(Landroid/content/Context;)V

    iput-object v1, p0, Lcom/nkvt/Menu;->drawView:Lcom/nkvt/DrawView;

    .line 81
    new-instance v7, Landroid/view/WindowManager$LayoutParams;

    const/4 v2, -0x1

    const/4 v3, -0x1

    const/16 v5, 0x438

    const/4 v6, -0x2

    move-object v1, v7

    move v4, v0

    invoke-direct/range {v1 .. v6}, Landroid/view/WindowManager$LayoutParams;-><init>(IIIII)V

    iput-object v7, p0, Lcom/nkvt/Menu;->windowManagerDrawViewParams:Landroid/view/WindowManager$LayoutParams;

    .line 82
    const/16 v1, 0x11

    iput v1, v7, Landroid/view/WindowManager$LayoutParams;->gravity:I

    .line 83
    iget-object v1, p0, Lcom/nkvt/Menu;->windowManager:Landroid/view/WindowManager;

    iget-object v2, p0, Lcom/nkvt/Menu;->drawView:Lcom/nkvt/DrawView;

    iget-object v3, p0, Lcom/nkvt/Menu;->windowManagerDrawViewParams:Landroid/view/WindowManager$LayoutParams;

    invoke-interface {v1, v2, v3}, Landroid/view/WindowManager;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 84
    return-void
.end method

.method public onCreate()V
    .locals 0

    .line 98
    invoke-virtual {p0}, Lcom/nkvt/Menu;->onCreateSystemWindow()V

    .line 99
    invoke-virtual {p0}, Lcom/nkvt/Menu;->onCreateTemplate()V

    .line 100
    return-void
.end method

.method public onCreateSystemWindow()V
    .locals 8

    .line 228
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x1a

    if-lt v0, v1, :cond_0

    .line 229
    const/16 v0, 0x7f6

    .local v0, "LAYOUT_FLAG":I
    goto :goto_0

    .line 231
    .end local v0    # "LAYOUT_FLAG":I
    :cond_0
    const/16 v0, 0x7d2

    .line 234
    .restart local v0    # "LAYOUT_FLAG":I
    :goto_0
    new-instance v1, Landroid/widget/FrameLayout;

    sget-object v2, Lcom/nkvt/Menu;->context:Landroid/content/Context;

    invoke-direct {v1, v2}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;)V

    iput-object v1, p0, Lcom/nkvt/Menu;->frameLayout:Landroid/widget/FrameLayout;

    .line 235
    new-instance v2, Landroid/widget/LinearLayout$LayoutParams;

    const/4 v3, -0x2

    invoke-direct {v2, v3, v3}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v1, v2}, Landroid/widget/FrameLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 236
    iget-object v1, p0, Lcom/nkvt/Menu;->frameLayout:Landroid/widget/FrameLayout;

    invoke-direct {p0}, Lcom/nkvt/Menu;->onTouchListener()Landroid/view/View$OnTouchListener;

    move-result-object v2

    invoke-virtual {v1, v2}, Landroid/widget/FrameLayout;->setOnTouchListener(Landroid/view/View$OnTouchListener;)V

    .line 237
    iget-object v1, p0, Lcom/nkvt/Menu;->frameLayout:Landroid/widget/FrameLayout;

    const v2, 0x3f4ccccd    # 0.8f

    invoke-virtual {v1, v2}, Landroid/widget/FrameLayout;->setAlpha(F)V

    .line 239
    new-instance v7, Landroid/view/WindowManager$LayoutParams;

    const/4 v2, -0x2

    const v5, 0x2820108

    const/4 v6, -0x2

    move-object v1, v7

    move v4, v0

    invoke-direct/range {v1 .. v6}, Landroid/view/WindowManager$LayoutParams;-><init>(IIIII)V

    iput-object v7, p0, Lcom/nkvt/Menu;->windowManagerParams:Landroid/view/WindowManager$LayoutParams;

    .line 240
    const/16 v1, 0x33

    iput v1, v7, Landroid/view/WindowManager$LayoutParams;->gravity:I

    .line 241
    iget-object v1, p0, Lcom/nkvt/Menu;->windowManagerParams:Landroid/view/WindowManager$LayoutParams;

    const/16 v2, 0xf

    iput v2, v1, Landroid/view/WindowManager$LayoutParams;->x:I

    .line 242
    iget-object v1, p0, Lcom/nkvt/Menu;->windowManagerParams:Landroid/view/WindowManager$LayoutParams;

    iput v2, v1, Landroid/view/WindowManager$LayoutParams;->y:I

    .line 244
    sget-object v1, Lcom/nkvt/Menu;->context:Landroid/content/Context;

    const-string v2, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->XtxQqﹶᵔʾⁱᵔˏˑᵢᴵﾞʿˈRNDLE()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroid/view/WindowManager;

    iput-object v1, p0, Lcom/nkvt/Menu;->windowManager:Landroid/view/WindowManager;

    .line 245
    invoke-virtual {p0}, Lcom/nkvt/Menu;->DrawCanvas()V

    .line 246
    iget-object v1, p0, Lcom/nkvt/Menu;->windowManager:Landroid/view/WindowManager;

    iget-object v2, p0, Lcom/nkvt/Menu;->frameLayout:Landroid/widget/FrameLayout;

    iget-object v3, p0, Lcom/nkvt/Menu;->windowManagerParams:Landroid/view/WindowManager$LayoutParams;

    invoke-interface {v1, v2, v3}, Landroid/view/WindowManager;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 247
    return-void
.end method

.method public onCreateTemplate()V
    .locals 19

    .line 104
    move-object/from16 v0, p0

    new-instance v1, Landroid/graphics/drawable/GradientDrawable;

    invoke-direct {v1}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    .line 105
    .local v1, "gradientDrawable_container":Landroid/graphics/drawable/GradientDrawable;
    const v2, -0xeeeeef

    invoke-virtual {v1, v2}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 106
    sget-object v3, Lcom/nkvt/Menu;->utils:Lcom/nkvt/Utils;

    const/16 v4, 0x8

    invoke-virtual {v3, v4}, Lcom/nkvt/Utils;->FixDP(I)I

    move-result v3

    int-to-float v3, v3

    invoke-virtual {v1, v3}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 108
    new-instance v3, Landroid/widget/LinearLayout;

    sget-object v5, Lcom/nkvt/Menu;->context:Landroid/content/Context;

    invoke-direct {v3, v5}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    .line 109
    .local v3, "container":Landroid/widget/LinearLayout;
    const/4 v5, 0x1

    invoke-virtual {v3, v5}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 111
    new-instance v6, Landroid/widget/LinearLayout;

    sget-object v7, Lcom/nkvt/Menu;->context:Landroid/content/Context;

    invoke-direct {v6, v7}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    .line 112
    .local v6, "container_menu":Landroid/widget/LinearLayout;
    new-instance v7, Landroid/widget/LinearLayout$LayoutParams;

    sget-object v8, Lcom/nkvt/Menu;->utils:Lcom/nkvt/Utils;

    const/16 v9, 0xd2

    invoke-virtual {v8, v9}, Lcom/nkvt/Utils;->FixDP(I)I

    move-result v8

    const/4 v9, -0x2

    invoke-direct {v7, v8, v9}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v6, v7}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 113
    invoke-virtual {v6, v2}, Landroid/widget/LinearLayout;->setBackgroundColor(I)V

    .line 114
    invoke-virtual {v6, v4}, Landroid/widget/LinearLayout;->setVisibility(I)V

    .line 115
    invoke-virtual {v6, v5}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 116
    invoke-virtual {v6, v1}, Landroid/widget/LinearLayout;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 118
    new-instance v7, Lcom/nkvt/ImageBase64;

    sget-object v8, Lcom/nkvt/Menu;->context:Landroid/content/Context;

    invoke-direct {v7, v8}, Lcom/nkvt/ImageBase64;-><init>(Landroid/content/Context;)V

    .line 119
    .local v7, "icon_cheat":Lcom/nkvt/ImageBase64;
    new-instance v8, Landroid/widget/LinearLayout$LayoutParams;

    sget-object v10, Lcom/nkvt/Menu;->utils:Lcom/nkvt/Utils;

    const/16 v11, 0x32

    invoke-virtual {v10, v11}, Lcom/nkvt/Utils;->FixDP(I)I

    move-result v10

    sget-object v12, Lcom/nkvt/Menu;->utils:Lcom/nkvt/Utils;

    invoke-virtual {v12, v11}, Lcom/nkvt/Utils;->FixDP(I)I

    move-result v11

    invoke-direct {v8, v10, v11}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v7, v8}, Lcom/nkvt/ImageBase64;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 120
    invoke-direct/range {p0 .. p0}, Lcom/nkvt/Menu;->imageBase64()Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v7, v8}, Lcom/nkvt/ImageBase64;->setImageBase64(Ljava/lang/String;)V

    .line 121
    invoke-direct/range {p0 .. p0}, Lcom/nkvt/Menu;->onTouchListener()Landroid/view/View$OnTouchListener;

    move-result-object v8

    invoke-virtual {v7, v8}, Lcom/nkvt/ImageBase64;->setOnTouchListener(Landroid/view/View$OnTouchListener;)V

    .line 122
    new-instance v8, Lcom/nkvt/Menu$1;

    invoke-direct {v8, v0, v7, v6}, Lcom/nkvt/Menu$1;-><init>(Lcom/nkvt/Menu;Lcom/nkvt/ImageBase64;Landroid/widget/LinearLayout;)V

    invoke-virtual {v7, v8}, Lcom/nkvt/ImageBase64;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 130
    new-instance v8, Landroid/widget/LinearLayout;

    sget-object v10, Lcom/nkvt/Menu;->context:Landroid/content/Context;

    invoke-direct {v8, v10}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    .line 131
    .local v8, "container_top":Landroid/widget/LinearLayout;
    new-instance v10, Landroid/widget/LinearLayout$LayoutParams;

    const/4 v11, -0x1

    invoke-direct {v10, v11, v9}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v8, v10}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 132
    sget-object v10, Lcom/nkvt/Menu;->utils:Lcom/nkvt/Utils;

    invoke-virtual {v10, v4}, Lcom/nkvt/Utils;->FixDP(I)I

    move-result v10

    sget-object v12, Lcom/nkvt/Menu;->utils:Lcom/nkvt/Utils;

    invoke-virtual {v12, v4}, Lcom/nkvt/Utils;->FixDP(I)I

    move-result v12

    sget-object v13, Lcom/nkvt/Menu;->utils:Lcom/nkvt/Utils;

    invoke-virtual {v13, v4}, Lcom/nkvt/Utils;->FixDP(I)I

    move-result v13

    sget-object v14, Lcom/nkvt/Menu;->utils:Lcom/nkvt/Utils;

    invoke-virtual {v14, v4}, Lcom/nkvt/Utils;->FixDP(I)I

    move-result v14

    invoke-virtual {v8, v10, v12, v13, v14}, Landroid/widget/LinearLayout;->setPadding(IIII)V

    .line 133
    const/16 v10, 0x11

    invoke-virtual {v8, v10}, Landroid/widget/LinearLayout;->setGravity(I)V

    .line 134
    const/4 v12, 0x0

    invoke-virtual {v8, v12}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 136
    new-instance v13, Lcom/nkvt/ImageBase64;

    sget-object v14, Lcom/nkvt/Menu;->context:Landroid/content/Context;

    invoke-direct {v13, v14}, Lcom/nkvt/ImageBase64;-><init>(Landroid/content/Context;)V

    .line 137
    .local v13, "icon_menu":Lcom/nkvt/ImageBase64;
    new-instance v14, Landroid/widget/LinearLayout$LayoutParams;

    sget-object v15, Lcom/nkvt/Menu;->utils:Lcom/nkvt/Utils;

    const/16 v2, 0x2d

    invoke-virtual {v15, v2}, Lcom/nkvt/Utils;->FixDP(I)I

    move-result v15

    sget-object v9, Lcom/nkvt/Menu;->utils:Lcom/nkvt/Utils;

    invoke-virtual {v9, v2}, Lcom/nkvt/Utils;->FixDP(I)I

    move-result v2

    invoke-direct {v14, v15, v2}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v13, v14}, Lcom/nkvt/ImageBase64;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 138
    invoke-direct/range {p0 .. p0}, Lcom/nkvt/Menu;->imageBase64()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v13, v2}, Lcom/nkvt/ImageBase64;->setImageBase64(Ljava/lang/String;)V

    .line 140
    new-instance v2, Landroid/widget/LinearLayout;

    sget-object v9, Lcom/nkvt/Menu;->context:Landroid/content/Context;

    invoke-direct {v2, v9}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    .line 141
    .local v2, "container_center":Landroid/widget/LinearLayout;
    new-instance v9, Landroid/widget/LinearLayout$LayoutParams;

    sget-object v14, Lcom/nkvt/Menu;->utils:Lcom/nkvt/Utils;

    const/16 v15, 0xb4

    invoke-virtual {v14, v15}, Lcom/nkvt/Utils;->FixDP(I)I

    move-result v14

    invoke-direct {v9, v11, v14}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v2, v9}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 142
    invoke-virtual {v2, v10}, Landroid/widget/LinearLayout;->setGravity(I)V

    .line 144
    new-instance v9, Landroid/widget/ScrollView;

    sget-object v10, Lcom/nkvt/Menu;->context:Landroid/content/Context;

    invoke-direct {v9, v10}, Landroid/widget/ScrollView;-><init>(Landroid/content/Context;)V

    .line 145
    .local v9, "scrollView_center":Landroid/widget/ScrollView;
    new-instance v10, Landroid/widget/LinearLayout$LayoutParams;

    invoke-direct {v10, v11, v11}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v9, v10}, Landroid/widget/ScrollView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 147
    new-instance v10, Landroid/widget/LinearLayout;

    sget-object v14, Lcom/nkvt/Menu;->context:Landroid/content/Context;

    invoke-direct {v10, v14}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    sput-object v10, Lcom/nkvt/Menu;->container_features:Landroid/widget/LinearLayout;

    .line 148
    new-instance v14, Landroid/widget/LinearLayout$LayoutParams;

    invoke-direct {v14, v11, v11}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v10, v14}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 149
    sget-object v10, Lcom/nkvt/Menu;->container_features:Landroid/widget/LinearLayout;

    sget-object v14, Lcom/nkvt/Menu;->utils:Lcom/nkvt/Utils;

    invoke-virtual {v14, v4}, Lcom/nkvt/Utils;->FixDP(I)I

    move-result v14

    sget-object v15, Lcom/nkvt/Menu;->utils:Lcom/nkvt/Utils;

    invoke-virtual {v15, v4}, Lcom/nkvt/Utils;->FixDP(I)I

    move-result v15

    invoke-virtual {v10, v14, v12, v15, v12}, Landroid/widget/LinearLayout;->setPadding(IIII)V

    .line 150
    sget-object v10, Lcom/nkvt/Menu;->container_features:Landroid/widget/LinearLayout;

    invoke-virtual {v10, v5}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 152
    new-instance v10, Landroid/widget/ProgressBar;

    sget-object v14, Lcom/nkvt/Menu;->context:Landroid/content/Context;

    invoke-direct {v10, v14}, Landroid/widget/ProgressBar;-><init>(Landroid/content/Context;)V

    .line 153
    .local v10, "progressBar":Landroid/widget/ProgressBar;
    new-instance v14, Landroid/widget/LinearLayout$LayoutParams;

    sget-object v15, Lcom/nkvt/Menu;->utils:Lcom/nkvt/Utils;

    const/16 v12, 0x28

    invoke-virtual {v15, v12}, Lcom/nkvt/Utils;->FixDP(I)I

    move-result v15

    sget-object v5, Lcom/nkvt/Menu;->utils:Lcom/nkvt/Utils;

    invoke-virtual {v5, v12}, Lcom/nkvt/Utils;->FixDP(I)I

    move-result v5

    invoke-direct {v14, v15, v5}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v10, v14}, Landroid/widget/ProgressBar;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 154
    invoke-virtual {v10}, Landroid/widget/ProgressBar;->getIndeterminateDrawable()Landroid/graphics/drawable/Drawable;

    move-result-object v5

    sget v12, Lcom/nkvt/Menu;->PrimaryColor:I

    sget-object v14, Landroid/graphics/PorterDuff$Mode;->SRC_IN:Landroid/graphics/PorterDuff$Mode;

    invoke-virtual {v5, v12, v14}, Landroid/graphics/drawable/Drawable;->setColorFilter(ILandroid/graphics/PorterDuff$Mode;)V

    .line 156
    new-instance v5, Landroid/widget/LinearLayout;

    sget-object v12, Lcom/nkvt/Menu;->context:Landroid/content/Context;

    invoke-direct {v5, v12}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    .line 157
    .local v5, "container_bottom":Landroid/widget/LinearLayout;
    new-instance v12, Landroid/widget/LinearLayout$LayoutParams;

    const/4 v14, -0x2

    invoke-direct {v12, v11, v14}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v5, v12}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 158
    sget-object v12, Lcom/nkvt/Menu;->utils:Lcom/nkvt/Utils;

    const/4 v14, 0x3

    invoke-virtual {v12, v14}, Lcom/nkvt/Utils;->FixDP(I)I

    move-result v12

    sget-object v15, Lcom/nkvt/Menu;->utils:Lcom/nkvt/Utils;

    invoke-virtual {v15, v4}, Lcom/nkvt/Utils;->FixDP(I)I

    move-result v15

    sget-object v11, Lcom/nkvt/Menu;->utils:Lcom/nkvt/Utils;

    invoke-virtual {v11, v14}, Lcom/nkvt/Utils;->FixDP(I)I

    move-result v11

    sget-object v4, Lcom/nkvt/Menu;->utils:Lcom/nkvt/Utils;

    invoke-virtual {v4, v14}, Lcom/nkvt/Utils;->FixDP(I)I

    move-result v4

    invoke-virtual {v5, v12, v15, v11, v4}, Landroid/widget/LinearLayout;->setPadding(IIII)V

    .line 159
    const/4 v4, 0x1

    invoke-virtual {v5, v4}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 160
    const/16 v4, 0x15

    invoke-virtual {v5, v4}, Landroid/widget/LinearLayout;->setGravity(I)V

    .line 162
    new-instance v4, Landroid/graphics/drawable/GradientDrawable;

    invoke-direct {v4}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    .line 163
    .local v4, "gradientDrawable_hide_close":Landroid/graphics/drawable/GradientDrawable;
    sget v11, Lcom/nkvt/Menu;->PrimaryColor:I

    invoke-virtual {v4, v11}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 164
    sget-object v11, Lcom/nkvt/Menu;->utils:Lcom/nkvt/Utils;

    const/16 v12, 0x8

    invoke-virtual {v11, v12}, Lcom/nkvt/Utils;->FixDP(I)I

    move-result v11

    int-to-float v11, v11

    invoke-virtual {v4, v11}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 165
    new-instance v11, Landroid/graphics/drawable/RippleDrawable;

    const v12, -0xeeeeef

    invoke-static {v12}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    move-result-object v14

    const/4 v12, 0x0

    invoke-direct {v11, v14, v4, v12}, Landroid/graphics/drawable/RippleDrawable;-><init>(Landroid/content/res/ColorStateList;Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;)V

    .line 168
    .local v11, "rippleDrawable1":Landroid/graphics/drawable/RippleDrawable;
    invoke-virtual {v2, v9}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 171
    new-instance v14, Landroid/graphics/drawable/GradientDrawable;

    invoke-direct {v14}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    .line 172
    .local v14, "gradientDrawable_inject_close":Landroid/graphics/drawable/GradientDrawable;
    sget v15, Lcom/nkvt/Menu;->PrimaryColor:I

    invoke-virtual {v14, v15}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 173
    sget-object v15, Lcom/nkvt/Menu;->utils:Lcom/nkvt/Utils;

    const/16 v12, 0x8

    invoke-virtual {v15, v12}, Lcom/nkvt/Utils;->FixDP(I)I

    move-result v12

    int-to-float v12, v12

    invoke-virtual {v14, v12}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 174
    new-instance v12, Landroid/graphics/drawable/RippleDrawable;

    const v15, -0xeeeeef

    invoke-static {v15}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    move-result-object v15

    move-object/from16 v16, v1

    const/4 v1, 0x0

    .end local v1    # "gradientDrawable_container":Landroid/graphics/drawable/GradientDrawable;
    .local v16, "gradientDrawable_container":Landroid/graphics/drawable/GradientDrawable;
    invoke-direct {v12, v15, v14, v1}, Landroid/graphics/drawable/RippleDrawable;-><init>(Landroid/content/res/ColorStateList;Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;)V

    move-object v1, v12

    .line 176
    .local v1, "rippleDrawable":Landroid/graphics/drawable/RippleDrawable;
    new-instance v12, Landroid/widget/Button;

    sget-object v15, Lcom/nkvt/Menu;->context:Landroid/content/Context;

    invoke-direct {v12, v15}, Landroid/widget/Button;-><init>(Landroid/content/Context;)V

    .line 177
    .local v12, "inject_close":Landroid/widget/Button;
    new-instance v15, Landroid/widget/LinearLayout$LayoutParams;

    move-object/from16 v17, v4

    .end local v4    # "gradientDrawable_hide_close":Landroid/graphics/drawable/GradientDrawable;
    .local v17, "gradientDrawable_hide_close":Landroid/graphics/drawable/GradientDrawable;
    sget-object v4, Lcom/nkvt/Menu;->utils:Lcom/nkvt/Utils;

    move-object/from16 v18, v10

    .end local v10    # "progressBar":Landroid/widget/ProgressBar;
    .local v18, "progressBar":Landroid/widget/ProgressBar;
    const/16 v10, 0x21

    invoke-virtual {v4, v10}, Lcom/nkvt/Utils;->FixDP(I)I

    move-result v4

    const/4 v10, -0x1

    invoke-direct {v15, v10, v4}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v12, v15}, Landroid/widget/Button;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 178
    const/4 v4, 0x0

    invoke-virtual {v12, v4, v4, v4, v4}, Landroid/widget/Button;->setPadding(IIII)V

    .line 179
    const-string v4, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->oQPexʽˏﹶˋᴵˊـʿˈـʾˏˑיˈٴˏⁱʻᐧvBIms()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v12, v4}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    .line 180
    const/high16 v4, 0x41200000    # 10.0f

    invoke-virtual {v12, v4}, Landroid/widget/Button;->setTextSize(F)V

    .line 181
    invoke-virtual {v12, v10}, Landroid/widget/Button;->setTextColor(I)V

    .line 182
    invoke-virtual {v12, v1}, Landroid/widget/Button;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 183
    new-instance v4, Lcom/nkvt/Menu$2;

    invoke-direct {v4, v0, v12, v7, v6}, Lcom/nkvt/Menu$2;-><init>(Lcom/nkvt/Menu;Landroid/widget/Button;Lcom/nkvt/ImageBase64;Landroid/widget/LinearLayout;)V

    invoke-virtual {v12, v4}, Landroid/widget/Button;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 205
    iget-object v4, v0, Lcom/nkvt/Menu;->frameLayout:Landroid/widget/FrameLayout;

    invoke-virtual {v4, v3}, Landroid/widget/FrameLayout;->addView(Landroid/view/View;)V

    .line 206
    invoke-virtual {v3, v7}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 207
    invoke-virtual {v3, v6}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 209
    invoke-virtual {v6, v8}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 210
    invoke-virtual {v8, v13}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 212
    invoke-virtual {v6, v2}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 215
    sget-object v4, Lcom/nkvt/Menu;->container_features:Landroid/widget/LinearLayout;

    invoke-virtual {v9, v4}, Landroid/widget/ScrollView;->addView(Landroid/view/View;)V

    .line 216
    new-instance v4, Landroid/view/View;

    sget-object v10, Lcom/nkvt/Menu;->context:Landroid/content/Context;

    invoke-direct {v4, v10}, Landroid/view/View;-><init>(Landroid/content/Context;)V

    .line 217
    .local v4, "spacer":Landroid/view/View;
    new-instance v10, Landroid/widget/LinearLayout$LayoutParams;

    sget-object v15, Lcom/nkvt/Menu;->utils:Lcom/nkvt/Utils;

    .line 218
    const/4 v0, 0x5

    invoke-virtual {v15, v0}, Lcom/nkvt/Utils;->FixDP(I)I

    move-result v0

    const/4 v15, -0x1

    invoke-direct {v10, v15, v0}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    .line 217
    invoke-virtual {v4, v10}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 220
    invoke-virtual {v6, v5}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 221
    invoke-virtual {v5, v12}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 223
    return-void
.end method
