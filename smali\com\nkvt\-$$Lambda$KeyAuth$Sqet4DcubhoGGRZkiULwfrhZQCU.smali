.class public final synthetic Lcom/nkvt/-$$Lambda$KeyAuth$Sqet4DcubhoGGRZkiULwfrhZQCU;
.super Ljava/lang/Object;
.source "lambda"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic f$0:Lcom/nkvt/KeyAuth;

.field public final synthetic f$1:Lcom/nkvt/KeyAuth$BanCallback;


# direct methods
.method public synthetic constructor <init>(Lcom/nkvt/KeyAuth;Lcom/nkvt/KeyAuth$BanCallback;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/nkvt/-$$Lambda$KeyAuth$Sqet4DcubhoGGRZkiULwfrhZQCU;->f$0:Lcom/nkvt/KeyAuth;

    iput-object p2, p0, Lcom/nkvt/-$$Lambda$KeyAuth$Sqet4DcubhoGGRZkiULwfrhZQCU;->f$1:Lcom/nkvt/KeyAuth$BanCallback;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    iget-object v0, p0, Lcom/nkvt/-$$Lambda$KeyAuth$Sqet4DcubhoGGRZkiULwfrhZQCU;->f$0:Lcom/nkvt/KeyAuth;

    iget-object v1, p0, Lcom/nkvt/-$$Lambda$KeyAuth$Sqet4DcubhoGGRZkiULwfrhZQCU;->f$1:Lcom/nkvt/KeyAuth$BanCallback;

    invoke-virtual {v0, v1}, Lcom/nkvt/KeyAuth;->lambda$ban$4$KeyAuth(Lcom/nkvt/KeyAuth$BanCallback;)V

    return-void
.end method
