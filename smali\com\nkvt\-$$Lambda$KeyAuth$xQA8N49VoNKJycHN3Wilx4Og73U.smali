.class public final synthetic Lcom/nkvt/-$$Lambda$KeyAuth$xQA8N49VoNKJycHN3Wilx4Og73U;
.super Ljava/lang/Object;
.source "lambda"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic f$0:Lcom/nkvt/KeyAuth;

.field public final synthetic f$1:Ljava/lang/String;

.field public final synthetic f$2:Ljava/lang/String;

.field public final synthetic f$3:Lcom/nkvt/KeyAuth$LoginCallback;


# direct methods
.method public synthetic constructor <init>(Lcom/nkvt/KeyAuth;Ljava/lang/String;Ljava/lang/String;Lcom/nkvt/KeyAuth$LoginCallback;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/nkvt/-$$Lambda$KeyAuth$xQA8N49VoNKJycHN3Wilx4Og73U;->f$0:Lcom/nkvt/KeyAuth;

    iput-object p2, p0, Lcom/nkvt/-$$Lambda$KeyAuth$xQA8N49VoNKJycHN3Wilx4Og73U;->f$1:Ljava/lang/String;

    iput-object p3, p0, Lcom/nkvt/-$$Lambda$KeyAuth$xQA8N49VoNKJycHN3Wilx4Og73U;->f$2:Ljava/lang/String;

    iput-object p4, p0, Lcom/nkvt/-$$Lambda$KeyAuth$xQA8N49VoNKJycHN3Wilx4Og73U;->f$3:Lcom/nkvt/KeyAuth$LoginCallback;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 4

    iget-object v0, p0, Lcom/nkvt/-$$Lambda$KeyAuth$xQA8N49VoNKJycHN3Wilx4Og73U;->f$0:Lcom/nkvt/KeyAuth;

    iget-object v1, p0, Lcom/nkvt/-$$Lambda$KeyAuth$xQA8N49VoNKJycHN3Wilx4Og73U;->f$1:Ljava/lang/String;

    iget-object v2, p0, Lcom/nkvt/-$$Lambda$KeyAuth$xQA8N49VoNKJycHN3Wilx4Og73U;->f$2:Ljava/lang/String;

    iget-object v3, p0, Lcom/nkvt/-$$Lambda$KeyAuth$xQA8N49VoNKJycHN3Wilx4Og73U;->f$3:Lcom/nkvt/KeyAuth$LoginCallback;

    invoke-virtual {v0, v1, v2, v3}, Lcom/nkvt/KeyAuth;->lambda$login$1$KeyAuth(Ljava/lang/String;Ljava/lang/String;Lcom/nkvt/KeyAuth$LoginCallback;)V

    return-void
.end method
