.class public Lcom/nkvt/Utils;
.super Ljava/lang/Object;
.source "Utils.java"


# instance fields
.field context:Landroid/content/Context;


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 0
    .param p1, "globContext"    # Landroid/content/Context;

    .line 10
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 11
    iput-object p1, p0, Lcom/nkvt/Utils;->context:Landroid/content/Context;

    .line 12
    return-void
.end method


# virtual methods
.method public FixDP(I)I
    .locals 3
    .param p1, "i"    # I

    .line 15
    int-to-float v0, p1

    iget-object v1, p0, Lcom/nkvt/Utils;->context:Landroid/content/Context;

    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    invoke-virtual {v1}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v1

    const/4 v2, 0x1

    invoke-static {v2, v0, v1}, Landroid/util/TypedValue;->applyDimension(IFLandroid/util/DisplayMetrics;)F

    move-result v0

    float-to-int v0, v0

    return v0
.end method
