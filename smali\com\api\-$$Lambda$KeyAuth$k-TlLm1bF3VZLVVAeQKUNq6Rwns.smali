.class public final synthetic Lcom/api/-$$Lambda$KeyAuth$k-TlLm1bF3VZLVVAeQKUNq6Rwns;
.super Ljava/lang/Object;
.source "lambda"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic f$0:Lcom/api/KeyAuth;

.field public final synthetic f$1:Lcom/api/KeyAuth$InitCallback;


# direct methods
.method public synthetic constructor <init>(Lcom/api/KeyAuth;Lcom/api/KeyAuth$InitCallback;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/api/-$$Lambda$KeyAuth$k-TlLm1bF3VZLVVAeQKUNq6Rwns;->f$0:Lcom/api/KeyAuth;

    iput-object p2, p0, Lcom/api/-$$Lambda$KeyAuth$k-TlLm1bF3VZLVVAeQKUNq6Rwns;->f$1:Lcom/api/KeyAuth$InitCallback;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    iget-object v0, p0, Lcom/api/-$$Lambda$KeyAuth$k-TlLm1bF3VZLVVAeQKUNq6Rwns;->f$0:Lcom/api/KeyAuth;

    iget-object v1, p0, Lcom/api/-$$Lambda$KeyAuth$k-TlLm1bF3VZLVVAeQKUNq6Rwns;->f$1:Lcom/api/KeyAuth$InitCallback;

    invoke-virtual {v0, v1}, Lcom/api/KeyAuth;->lambda$init$0$KeyAuth(Lcom/api/KeyAuth$InitCallback;)V

    return-void
.end method
