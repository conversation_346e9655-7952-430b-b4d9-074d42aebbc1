.class public Lcom/nkvt/ImageString;
.super Ljava/lang/Object;
.source "ImageString.java"


# instance fields
.field public icon_image:Ljava/lang/String;


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 5
    const-string v0, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->SSxmyˊﹳˎˈᴵʽˉˎʾʾˏˈˈˉˉʾRdBRq()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/nkvt/ImageString;->icon_image:Ljava/lang/String;

    return-void
.end method
