.class public final synthetic Lcom/api/-$$Lambda$KeyAuth$e1l2clvtOP-ayd03aVUoJlK_V_8;
.super Ljava/lang/Object;
.source "lambda"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic f$0:Lcom/api/KeyAuth;

.field public final synthetic f$1:Lcom/api/KeyAuth$BanCallback;


# direct methods
.method public synthetic constructor <init>(Lcom/api/KeyAuth;Lcom/api/KeyAuth$BanCallback;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/api/-$$Lambda$KeyAuth$e1l2clvtOP-ayd03aVUoJlK_V_8;->f$0:Lcom/api/KeyAuth;

    iput-object p2, p0, Lcom/api/-$$Lambda$KeyAuth$e1l2clvtOP-ayd03aVUoJlK_V_8;->f$1:Lcom/api/KeyAuth$BanCallback;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    iget-object v0, p0, Lcom/api/-$$Lambda$KeyAuth$e1l2clvtOP-ayd03aVUoJlK_V_8;->f$0:Lcom/api/KeyAuth;

    iget-object v1, p0, Lcom/api/-$$Lambda$KeyAuth$e1l2clvtOP-ayd03aVUoJlK_V_8;->f$1:Lcom/api/KeyAuth$BanCallback;

    invoke-virtual {v0, v1}, Lcom/api/KeyAuth;->lambda$ban$4$KeyAuth(Lcom/api/KeyAuth$BanCallback;)V

    return-void
.end method
