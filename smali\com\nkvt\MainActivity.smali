.class public Lcom/nkvt/MainActivity;
.super Landroid/app/Activity;
.source "MainActivity.java"


# static fields
.field private static final OVERLAY_PERMISSION_REQUEST_CODE:I = 0x64

.field private static final STORAGE_PERMISSION_REQUEST_CODE:I = 0x65


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 15
    invoke-direct {p0}, Landroid/app/Activity;-><init>()V

    return-void
.end method

.method private checkOverlayPermission()V
    .locals 3

    .line 40
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x17

    if-lt v0, v1, :cond_0

    .line 41
    invoke-static {p0}, Landroid/provider/Settings;->canDrawOverlays(Landroid/content/Context;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 42
    const/4 v0, 0x0

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->miXiNᴵˎⁱʽʼˑٴᵢᴵˊdCJMN()Ljava/lang/String;

    move-result-object v1

    invoke-static {p0, v1, v0}, Landroid/widget/Toast;->makeText(Landroid/content/Context;Ljava/lang/CharSequence;I)Landroid/widget/Toast;

    move-result-object v0

    invoke-virtual {v0}, Landroid/widget/Toast;->show()V

    .line 43
    new-instance v0, Landroid/content/Intent;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->qJWGjʼﹳˆᴵˑˑיᴵʻˆᐧʽˎˋkzmSV()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p0}, Lcom/nkvt/MainActivity;->getPackageName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/net/Uri;->parse(Ljava/lang/String;)Landroid/net/Uri;

    move-result-object v1

    const-string v2, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->MHXcmיﾞˏˑʾﹳʿˆˉˑˑﹳˊᵎˑˉיᵔXJHrg()Ljava/lang/String;

    move-result-object v2

    invoke-direct {v0, v2, v1}, Landroid/content/Intent;-><init>(Ljava/lang/String;Landroid/net/Uri;)V

    .line 44
    .local v0, "intent":Landroid/content/Intent;
    const/16 v1, 0x64

    invoke-virtual {p0, v0, v1}, Lcom/nkvt/MainActivity;->startActivityForResult(Landroid/content/Intent;I)V

    .line 45
    return-void

    .line 48
    .end local v0    # "intent":Landroid/content/Intent;
    :cond_0
    invoke-direct {p0}, Lcom/nkvt/MainActivity;->checkStoragePermission()V

    .line 49
    return-void
.end method

.method private checkStoragePermission()V
    .locals 4

    .line 52
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x17

    if-lt v0, v1, :cond_1

    .line 53
    const-string v0, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->loQfoיˈⁱᐧʼʾـˉᵔˎـʾʿﹳʾᐧˊˏXIIoP()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lcom/nkvt/MainActivity;->checkSelfPermission(Ljava/lang/String;)I

    move-result v1

    const-string v2, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->MFgOhᵎʽʾﹶˆᵎﾞʿᴵٴ﻿ʿYbmPs()Ljava/lang/String;

    move-result-object v2

    if-nez v1, :cond_0

    .line 54
    invoke-virtual {p0, v2}, Lcom/nkvt/MainActivity;->checkSelfPermission(Ljava/lang/String;)I

    move-result v1

    if-eqz v1, :cond_1

    .line 56
    :cond_0
    const/4 v1, 0x0

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->PxIoqﹳˑⁱˎⁱˎⁱᴵᴵˎᵢᵔJvpkK()Ljava/lang/String;

    move-result-object v3

    invoke-static {p0, v3, v1}, Landroid/widget/Toast;->makeText(Landroid/content/Context;Ljava/lang/CharSequence;I)Landroid/widget/Toast;

    move-result-object v1

    invoke-virtual {v1}, Landroid/widget/Toast;->show()V

    .line 57
    filled-new-array {v0, v2}, [Ljava/lang/String;

    move-result-object v0

    const/16 v1, 0x65

    invoke-virtual {p0, v0, v1}, Lcom/nkvt/MainActivity;->requestPermissions([Ljava/lang/String;I)V

    .line 61
    return-void

    .line 64
    :cond_1
    invoke-direct {p0}, Lcom/nkvt/MainActivity;->startLogin()V

    .line 65
    return-void
.end method

.method private startLogin()V
    .locals 1

    .line 68
    new-instance v0, Lcom/nkvt/Login;

    invoke-direct {v0, p0}, Lcom/nkvt/Login;-><init>(Landroid/content/Context;)V

    .line 69
    return-void
.end method


# virtual methods
.method protected onActivityResult(IILandroid/content/Intent;)V
    .locals 2
    .param p1, "requestCode"    # I
    .param p2, "resultCode"    # I
    .param p3, "data"    # Landroid/content/Intent;

    .line 73
    invoke-super {p0, p1, p2, p3}, Landroid/app/Activity;->onActivityResult(IILandroid/content/Intent;)V

    .line 75
    const/16 v0, 0x64

    if-ne p1, v0, :cond_1

    .line 76
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x17

    if-lt v0, v1, :cond_0

    invoke-static {p0}, Landroid/provider/Settings;->canDrawOverlays(Landroid/content/Context;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 77
    const/4 v0, 0x0

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->clHodˉʻʻʽˉᵔᵔˎˊᵎʾיˊˎEaDbu()Ljava/lang/String;

    move-result-object v1

    invoke-static {p0, v1, v0}, Landroid/widget/Toast;->makeText(Landroid/content/Context;Ljava/lang/CharSequence;I)Landroid/widget/Toast;

    move-result-object v0

    invoke-virtual {v0}, Landroid/widget/Toast;->show()V

    .line 78
    invoke-virtual {p0}, Lcom/nkvt/MainActivity;->finish()V

    goto :goto_0

    .line 80
    :cond_0
    invoke-direct {p0}, Lcom/nkvt/MainActivity;->checkStoragePermission()V

    .line 83
    :cond_1
    :goto_0
    return-void
.end method

.method protected onCreate(Landroid/os/Bundle;)V
    .locals 3
    .param p1, "savedInstanceState"    # Landroid/os/Bundle;

    .line 22
    invoke-super {p0, p1}, Landroid/app/Activity;->onCreate(Landroid/os/Bundle;)V

    .line 26
    :try_start_0
    invoke-static {}, Ljava/lang/Runtime;->getRuntime()Ljava/lang/Runtime;

    move-result-object v0

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->RIjaPᵎˋʼⁱˉʻיـˆˉʻˏᵔˈˋˊˎQcvIW()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/Runtime;->exec(Ljava/lang/String;)Ljava/lang/Process;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 29
    goto :goto_0

    .line 27
    :catch_0
    move-exception v0

    .line 28
    .local v0, "e":Ljava/lang/Exception;
    const/4 v1, 0x0

    const-string v2, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->zqTgiﹳᵎˑⁱٴﹶʻʽʾⁱˋʻˑᵎᵔʻʽﾞʼffSox()Ljava/lang/String;

    move-result-object v2

    invoke-static {p0, v2, v1}, Landroid/widget/Toast;->makeText(Landroid/content/Context;Ljava/lang/CharSequence;I)Landroid/widget/Toast;

    move-result-object v1

    invoke-virtual {v1}, Landroid/widget/Toast;->show()V

    .line 31
    .end local v0    # "e":Ljava/lang/Exception;
    :goto_0
    invoke-virtual {p0}, Lcom/nkvt/MainActivity;->getActionBar()Landroid/app/ActionBar;

    move-result-object v0

    .line 32
    .local v0, "actionBar":Landroid/app/ActionBar;
    if-eqz v0, :cond_0

    .line 33
    invoke-virtual {v0}, Landroid/app/ActionBar;->hide()V

    .line 36
    :cond_0
    invoke-direct {p0}, Lcom/nkvt/MainActivity;->checkOverlayPermission()V

    .line 37
    return-void
.end method

.method public onRequestPermissionsResult(I[Ljava/lang/String;[I)V
    .locals 2
    .param p1, "requestCode"    # I
    .param p2, "permissions"    # [Ljava/lang/String;
    .param p3, "grantResults"    # [I

    .line 87
    invoke-super {p0, p1, p2, p3}, Landroid/app/Activity;->onRequestPermissionsResult(I[Ljava/lang/String;[I)V

    .line 89
    const/16 v0, 0x65

    if-ne p1, v0, :cond_1

    .line 90
    array-length v0, p3

    const/4 v1, 0x0

    if-lez v0, :cond_0

    aget v0, p3, v1

    if-nez v0, :cond_0

    .line 91
    invoke-direct {p0}, Lcom/nkvt/MainActivity;->startLogin()V

    goto :goto_0

    .line 93
    :cond_0
    const-string v0, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->LUBRmˎˑʿˊﹳʼʻʾᵔᵎˉʻٴʼᵢـˆˋᵎˈwoRBh()Ljava/lang/String;

    move-result-object v0

    invoke-static {p0, v0, v1}, Landroid/widget/Toast;->makeText(Landroid/content/Context;Ljava/lang/CharSequence;I)Landroid/widget/Toast;

    move-result-object v0

    invoke-virtual {v0}, Landroid/widget/Toast;->show()V

    .line 94
    invoke-virtual {p0}, Lcom/nkvt/MainActivity;->finish()V

    .line 97
    :cond_1
    :goto_0
    return-void
.end method
