.class public Lcom/topjohnwu/superuser/internal/RootServiceServer;
.super Lcom/topjohnwu/superuser/internal/IRootServiceManager$Stub;

# interfaces
.implements Landroid/os/IBinder$DeathRecipient;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/topjohnwu/superuser/internal/RootServiceServer$AppObserver;,
        Lcom/topjohnwu/superuser/internal/RootServiceServer$ServiceContainer;
    }
.end annotation


# static fields
.field private static mInstance:Lcom/topjohnwu/superuser/internal/RootServiceServer;


# instance fields
.field private final activeServices:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Landroid/content/ComponentName;",
            "Lcom/topjohnwu/superuser/internal/RootServiceServer$ServiceContainer;",
            ">;"
        }
    .end annotation
.end field

.field private client:Landroid/os/Messenger;

.field private isDaemon:Z

.field private mAction:Ljava/lang/String;

.field private final observer:Landroid/os/FileObserver;


# direct methods
.method private constructor <init>(Landroid/content/Context;)V
    .locals 2

    invoke-direct {p0}, Lcom/topjohnwu/superuser/internal/IRootServiceManager$Stub;-><init>()V

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/topjohnwu/superuser/internal/RootServiceServer;->isDaemon:Z

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->xHDCFˊˑˊʿﾞᴵـˋˈˑـˈʽᵢˆٴᵎﹳˉᴵˊʽetBJz()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Ljava/lang/System;->getenv(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    if-eqz v1, :cond_0

    const/4 v0, 0x1

    :cond_0
    sput-boolean v0, Lcom/topjohnwu/superuser/Shell;->enableVerboseLogging:Z

    const-string v0, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->DXgBnˉﾞʽﹳʾˋᵢיʿٴˋʾʾᐧᴵᵎᵎˆtZEzF()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Ljava/lang/System;->getenv(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/topjohnwu/superuser/internal/RootServiceServer;->mAction:Ljava/lang/String;

    sput-object p1, Lcom/topjohnwu/superuser/internal/Utils;->context:Landroid/content/Context;

    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x13

    if-lt v0, v1, :cond_1

    new-instance v0, Landroid/util/ArrayMap;

    invoke-direct {v0}, Landroid/util/ArrayMap;-><init>()V

    goto :goto_0

    :cond_1
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    :goto_0
    iput-object v0, p0, Lcom/topjohnwu/superuser/internal/RootServiceServer;->activeServices:Ljava/util/Map;

    new-instance v0, Lcom/topjohnwu/superuser/internal/RootServiceServer$AppObserver;

    new-instance v1, Ljava/io/File;

    invoke-virtual {p1}, Landroid/content/Context;->getPackageCodePath()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v1, p1}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-direct {v0, p0, v1}, Lcom/topjohnwu/superuser/internal/RootServiceServer$AppObserver;-><init>(Lcom/topjohnwu/superuser/internal/RootServiceServer;Ljava/io/File;)V

    iput-object v0, p0, Lcom/topjohnwu/superuser/internal/RootServiceServer;->observer:Landroid/os/FileObserver;

    invoke-virtual {p0}, Lcom/topjohnwu/superuser/internal/RootServiceServer;->broadcast()V

    invoke-virtual {v0}, Landroid/os/FileObserver;->startWatching()V

    return-void
.end method

.method static synthetic access$000(Lcom/topjohnwu/superuser/internal/RootServiceServer;Z)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/topjohnwu/superuser/internal/RootServiceServer;->stopAllService(Z)V

    return-void
.end method

.method private bindInternal(Landroid/content/Intent;)Landroid/os/IBinder;
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    invoke-virtual {p1}, Landroid/content/Intent;->getComponent()Landroid/content/ComponentName;

    move-result-object v0

    iget-object v1, p0, Lcom/topjohnwu/superuser/internal/RootServiceServer;->activeServices:Ljava/util/Map;

    invoke-interface {v1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/topjohnwu/superuser/internal/RootServiceServer$ServiceContainer;

    if-nez v1, :cond_0

    invoke-virtual {v0}, Landroid/content/ComponentName;->getClassName()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Ljava/lang/Class;->forName(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v1

    const/4 v2, 0x0

    new-array v3, v2, [Ljava/lang/Class;

    invoke-virtual {v1, v3}, Ljava/lang/Class;->getDeclaredConstructor([Ljava/lang/Class;)Ljava/lang/reflect/Constructor;

    move-result-object v1

    const/4 v3, 0x1

    invoke-virtual {v1, v3}, Ljava/lang/reflect/Constructor;->setAccessible(Z)V

    sget-object v4, Lcom/topjohnwu/superuser/internal/RootServerMain;->attachBaseContext:Ljava/lang/reflect/Method;

    new-array v5, v2, [Ljava/lang/Object;

    invoke-virtual {v1, v5}, Ljava/lang/reflect/Constructor;->newInstance([Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    new-array v3, v3, [Ljava/lang/Object;

    sget-object v5, Lcom/topjohnwu/superuser/internal/Utils;->context:Landroid/content/Context;

    aput-object v5, v3, v2

    invoke-virtual {v4, v1, v3}, Ljava/lang/reflect/Method;->invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v1, p0, Lcom/topjohnwu/superuser/internal/RootServiceServer;->activeServices:Ljava/util/Map;

    invoke-interface {v1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/topjohnwu/superuser/internal/RootServiceServer$ServiceContainer;

    if-nez v1, :cond_0

    const/4 p1, 0x0

    return-object p1

    :cond_0
    iget-object v2, v1, Lcom/topjohnwu/superuser/internal/RootServiceServer$ServiceContainer;->binder:Landroid/os/IBinder;

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->FduPMـʿʻﾞˈˉˊˋˏٴـᵔˎ﻿ʿⁱᴵfkwpT()Ljava/lang/String;

    move-result-object v3

    if-eqz v2, :cond_1

    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0}, Landroid/content/ComponentName;->getClassName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->ZZRjfʻˈᴵـﹳᵎᵔʽˆٴﾞٴMCtIc()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v3, p1}, Lcom/topjohnwu/superuser/internal/Utils;->log(Ljava/lang/String;Ljava/lang/Object;)V

    iget-object p1, v1, Lcom/topjohnwu/superuser/internal/RootServiceServer$ServiceContainer;->service:Lcom/topjohnwu/superuser/ipc/RootService;

    iget-object v0, v1, Lcom/topjohnwu/superuser/internal/RootServiceServer$ServiceContainer;->intent:Landroid/content/Intent;

    invoke-virtual {p1, v0}, Lcom/topjohnwu/superuser/ipc/RootService;->onRebind(Landroid/content/Intent;)V

    goto :goto_0

    :cond_1
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0}, Landroid/content/ComponentName;->getClassName()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->ZrcZOᵔʻˏٴˋˏᵢˉᵔﹶٴˆﹳˋTScxd()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v3, v0}, Lcom/topjohnwu/superuser/internal/Utils;->log(Ljava/lang/String;Ljava/lang/Object;)V

    iget-object v0, v1, Lcom/topjohnwu/superuser/internal/RootServiceServer$ServiceContainer;->service:Lcom/topjohnwu/superuser/ipc/RootService;

    invoke-virtual {v0, p1}, Lcom/topjohnwu/superuser/ipc/RootService;->onBind(Landroid/content/Intent;)Landroid/os/IBinder;

    move-result-object v0

    iput-object v0, v1, Lcom/topjohnwu/superuser/internal/RootServiceServer$ServiceContainer;->binder:Landroid/os/IBinder;

    invoke-virtual {p1}, Landroid/content/Intent;->cloneFilter()Landroid/content/Intent;

    move-result-object p1

    iput-object p1, v1, Lcom/topjohnwu/superuser/internal/RootServiceServer$ServiceContainer;->intent:Landroid/content/Intent;

    :goto_0
    iget-object p1, v1, Lcom/topjohnwu/superuser/internal/RootServiceServer$ServiceContainer;->binder:Landroid/os/IBinder;

    return-object p1
.end method

.method public static getInstance(Landroid/content/Context;)Lcom/topjohnwu/superuser/internal/RootServiceServer;
    .locals 1

    sget-object v0, Lcom/topjohnwu/superuser/internal/RootServiceServer;->mInstance:Lcom/topjohnwu/superuser/internal/RootServiceServer;

    if-nez v0, :cond_0

    new-instance v0, Lcom/topjohnwu/superuser/internal/RootServiceServer;

    invoke-direct {v0, p0}, Lcom/topjohnwu/superuser/internal/RootServiceServer;-><init>(Landroid/content/Context;)V

    sput-object v0, Lcom/topjohnwu/superuser/internal/RootServiceServer;->mInstance:Lcom/topjohnwu/superuser/internal/RootServiceServer;

    :cond_0
    sget-object p0, Lcom/topjohnwu/superuser/internal/RootServiceServer;->mInstance:Lcom/topjohnwu/superuser/internal/RootServiceServer;

    return-object p0
.end method

.method private setAsDaemon()V
    .locals 1

    iget-boolean v0, p0, Lcom/topjohnwu/superuser/internal/RootServiceServer;->isDaemon:Z

    if-nez v0, :cond_0

    sget-object v0, Lcom/topjohnwu/superuser/internal/Utils;->context:Landroid/content/Context;

    invoke-virtual {v0}, Landroid/content/Context;->getPackageName()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/topjohnwu/superuser/internal/RootServerMain;->getServiceName(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v0, p0}, Lcom/topjohnwu/superuser/internal/HiddenAPIs;->addService(Ljava/lang/String;Landroid/os/IBinder;)V

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/topjohnwu/superuser/internal/RootServiceServer;->isDaemon:Z

    :cond_0
    return-void
.end method

.method private stopAllService(Z)V
    .locals 4

    iget-object v0, p0, Lcom/topjohnwu/superuser/internal/RootServiceServer;->activeServices:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/Map$Entry;

    invoke-interface {v1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/topjohnwu/superuser/internal/RootServiceServer$ServiceContainer;

    iget-object v2, v1, Lcom/topjohnwu/superuser/internal/RootServiceServer$ServiceContainer;->service:Lcom/topjohnwu/superuser/ipc/RootService;

    iget-object v3, v1, Lcom/topjohnwu/superuser/internal/RootServiceServer$ServiceContainer;->intent:Landroid/content/Intent;

    invoke-virtual {v2, v3}, Lcom/topjohnwu/superuser/ipc/RootService;->onUnbind(Landroid/content/Intent;)Z

    move-result v2

    if-eqz v2, :cond_1

    if-eqz p1, :cond_0

    goto :goto_1

    :cond_0
    invoke-direct {p0}, Lcom/topjohnwu/superuser/internal/RootServiceServer;->setAsDaemon()V

    goto :goto_0

    :cond_1
    :goto_1
    iget-object v1, v1, Lcom/topjohnwu/superuser/internal/RootServiceServer$ServiceContainer;->service:Lcom/topjohnwu/superuser/ipc/RootService;

    invoke-virtual {v1}, Lcom/topjohnwu/superuser/ipc/RootService;->onDestroy()V

    invoke-interface {v0}, Ljava/util/Iterator;->remove()V

    goto :goto_0

    :cond_2
    if-nez p1, :cond_3

    iget-object p1, p0, Lcom/topjohnwu/superuser/internal/RootServiceServer;->activeServices:Ljava/util/Map;

    invoke-interface {p1}, Ljava/util/Map;->isEmpty()Z

    move-result p1

    if-eqz p1, :cond_4

    :cond_3
    const/4 p1, 0x0

    invoke-static {p1}, Ljava/lang/System;->exit(I)V

    :cond_4
    return-void
.end method

.method private stopService(Landroid/content/ComponentName;Z)V
    .locals 3

    iget-object v0, p0, Lcom/topjohnwu/superuser/internal/RootServiceServer;->activeServices:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/topjohnwu/superuser/internal/RootServiceServer$ServiceContainer;

    if-eqz v0, :cond_2

    iget-object v1, v0, Lcom/topjohnwu/superuser/internal/RootServiceServer$ServiceContainer;->service:Lcom/topjohnwu/superuser/ipc/RootService;

    iget-object v2, v0, Lcom/topjohnwu/superuser/internal/RootServiceServer$ServiceContainer;->intent:Landroid/content/Intent;

    invoke-virtual {v1, v2}, Lcom/topjohnwu/superuser/ipc/RootService;->onUnbind(Landroid/content/Intent;)Z

    move-result v1

    if-eqz v1, :cond_1

    if-eqz p2, :cond_0

    goto :goto_0

    :cond_0
    invoke-direct {p0}, Lcom/topjohnwu/superuser/internal/RootServiceServer;->setAsDaemon()V

    goto :goto_1

    :cond_1
    :goto_0
    iget-object p2, v0, Lcom/topjohnwu/superuser/internal/RootServiceServer$ServiceContainer;->service:Lcom/topjohnwu/superuser/ipc/RootService;

    invoke-virtual {p2}, Lcom/topjohnwu/superuser/ipc/RootService;->onDestroy()V

    iget-object p2, p0, Lcom/topjohnwu/superuser/internal/RootServiceServer;->activeServices:Ljava/util/Map;

    invoke-interface {p2, p1}, Ljava/util/Map;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    :cond_2
    :goto_1
    iget-object p1, p0, Lcom/topjohnwu/superuser/internal/RootServiceServer;->activeServices:Ljava/util/Map;

    invoke-interface {p1}, Ljava/util/Map;->isEmpty()Z

    move-result p1

    if-eqz p1, :cond_3

    const/4 p1, 0x0

    invoke-static {p1}, Ljava/lang/System;->exit(I)V

    :cond_3
    return-void
.end method


# virtual methods
.method public bind(Landroid/content/Intent;)Landroid/os/IBinder;
    .locals 2

    const/4 v0, 0x1

    new-array v0, v0, [Landroid/os/IBinder;

    new-instance v1, Lcom/topjohnwu/superuser/internal/RootServiceServer$$ExternalSyntheticLambda4;

    invoke-direct {v1, p0, v0, p1}, Lcom/topjohnwu/superuser/internal/RootServiceServer$$ExternalSyntheticLambda4;-><init>(Lcom/topjohnwu/superuser/internal/RootServiceServer;[Landroid/os/IBinder;Landroid/content/Intent;)V

    invoke-static {v1}, Lcom/topjohnwu/superuser/internal/UiThreadHandler;->runAndWait(Ljava/lang/Runnable;)V

    const/4 p1, 0x0

    aget-object p1, v0, p1

    return-object p1
.end method

.method public binderDied()V
    .locals 2

    iget-object v0, p0, Lcom/topjohnwu/superuser/internal/RootServiceServer;->client:Landroid/os/Messenger;

    const/4 v1, 0x0

    iput-object v1, p0, Lcom/topjohnwu/superuser/internal/RootServiceServer;->client:Landroid/os/Messenger;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Landroid/os/Messenger;->getBinder()Landroid/os/IBinder;

    move-result-object v0

    const/4 v1, 0x0

    invoke-interface {v0, p0, v1}, Landroid/os/IBinder;->unlinkToDeath(Landroid/os/IBinder$DeathRecipient;I)Z

    :cond_0
    new-instance v0, Lcom/topjohnwu/superuser/internal/RootServiceServer$$ExternalSyntheticLambda0;

    invoke-direct {v0, p0}, Lcom/topjohnwu/superuser/internal/RootServiceServer$$ExternalSyntheticLambda0;-><init>(Lcom/topjohnwu/superuser/internal/RootServiceServer;)V

    invoke-static {v0}, Lcom/topjohnwu/superuser/internal/UiThreadHandler;->run(Ljava/lang/Runnable;)V

    return-void
.end method

.method public broadcast()V
    .locals 2

    sget-object v0, Lcom/topjohnwu/superuser/internal/Utils;->context:Landroid/content/Context;

    iget-object v1, p0, Lcom/topjohnwu/superuser/internal/RootServiceServer;->mAction:Ljava/lang/String;

    invoke-static {v0, v1, p0}, Lcom/topjohnwu/superuser/internal/RootServiceManager;->getBroadcastIntent(Landroid/content/Context;Ljava/lang/String;Landroid/os/IBinder;)Landroid/content/Intent;

    move-result-object v0

    sget-object v1, Lcom/topjohnwu/superuser/internal/Utils;->context:Landroid/content/Context;

    invoke-virtual {v1, v0}, Landroid/content/Context;->sendBroadcast(Landroid/content/Intent;)V

    return-void
.end method

.method public connect(Landroid/os/Bundle;)V
    .locals 4

    const-string v0, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->wxWFfʽʿٴʻﾞˎˈᵎʾˎⁱˉˑˋʿʿﾞﹳʿﹶZulPw()Ljava/lang/String;

    move-result-object v0

    iget-object v1, p0, Lcom/topjohnwu/superuser/internal/RootServiceServer;->client:Landroid/os/Messenger;

    if-eqz v1, :cond_0

    return-void

    :cond_0
    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->DvIFpˑٴˏᵢˎˎˊˎˎﹳיٴﾞיˈٴʽٴtTuyF()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1, v1}, Landroid/os/Bundle;->getBinder(Ljava/lang/String;)Landroid/os/IBinder;

    move-result-object v1

    if-nez v1, :cond_1

    return-void

    :cond_1
    const/4 v2, 0x0

    :try_start_0
    invoke-interface {v1, p0, v2}, Landroid/os/IBinder;->linkToDeath(Landroid/os/IBinder$DeathRecipient;I)V

    new-instance v3, Landroid/os/Messenger;

    invoke-direct {v3, v1}, Landroid/os/Messenger;-><init>(Landroid/os/IBinder;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_2

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->CSNnxᵔˈﹶᴵⁱʻʽᴵᐧﹶﹳʽnfKju()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1, v1, v2}, Landroid/os/Bundle;->getBoolean(Ljava/lang/String;Z)Z

    move-result p1

    if-eqz p1, :cond_3

    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    sget-object v1, Lcom/topjohnwu/superuser/internal/Utils;->context:Landroid/content/Context;

    invoke-virtual {v1}, Landroid/content/Context;->getPackageName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->uzHMAⁱⁱˎʻˊʽʻˈﾞﾞⁱˉˉﾞˆᐧᐧﾞˆᐧﹳˎˆﾞٴRKiWH()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Lcom/topjohnwu/superuser/internal/HiddenAPIs;->setAppName(Ljava/lang/String;)V

    const-string p1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->MaueSʿʻˑᵢˋᵔʿʻˋˆᵎﹳˆﹳiqlmF()Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Lcom/topjohnwu/superuser/internal/Utils;->log(Ljava/lang/String;Ljava/lang/Object;)V

    :goto_0
    invoke-static {}, Landroid/os/Debug;->isDebuggerConnected()Z

    move-result p1

    if-nez p1, :cond_2

    const-wide/16 v1, 0xc8

    :try_start_1
    invoke-static {v1, v2}, Ljava/lang/Thread;->sleep(J)V
    :try_end_1
    .catch Ljava/lang/InterruptedException; {:try_start_1 .. :try_end_1} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    goto :goto_0

    :cond_2
    const-string p1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->VFwpBᵔˏᴵٴˑﹳﾞʿˑᴵﹶʽˏˏerwiQ()Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Lcom/topjohnwu/superuser/internal/Utils;->log(Ljava/lang/String;Ljava/lang/Object;)V

    :cond_3
    invoke-static {}, Landroid/os/Message;->obtain()Landroid/os/Message;

    move-result-object p1

    const/4 v0, 0x1

    iput v0, p1, Landroid/os/Message;->what:I

    :try_start_2
    invoke-virtual {v3, p1}, Landroid/os/Messenger;->send(Landroid/os/Message;)V

    iput-object v3, p0, Lcom/topjohnwu/superuser/internal/RootServiceServer;->client:Landroid/os/Messenger;
    :try_end_2
    .catch Landroid/os/RemoteException; {:try_start_2 .. :try_end_2} :catch_1

    goto :goto_1

    :catch_1
    move-exception p1

    :goto_1
    return-void

    :catch_2
    move-exception p1

    invoke-static {v0, p1}, Lcom/topjohnwu/superuser/internal/Utils;->err(Ljava/lang/String;Ljava/lang/Throwable;)V

    return-void
.end method

.method synthetic lambda$bind$0$com-topjohnwu-superuser-internal-RootServiceServer([Landroid/os/IBinder;Landroid/content/Intent;)V
    .locals 1

    const/4 v0, 0x0

    :try_start_0
    invoke-direct {p0, p2}, Lcom/topjohnwu/superuser/internal/RootServiceServer;->bindInternal(Landroid/content/Intent;)Landroid/os/IBinder;

    move-result-object p2

    aput-object p2, p1, v0
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    const-string p2, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->eDrsIˆᐧיᴵⁱᵔᐧʿיˉʻˊⁱˏʿﹳʾᵎʽﹳnrgSx()Ljava/lang/String;

    move-result-object p2

    invoke-static {p2, p1}, Lcom/topjohnwu/superuser/internal/Utils;->err(Ljava/lang/String;Ljava/lang/Throwable;)V

    :goto_0
    return-void
.end method

.method synthetic lambda$binderDied$3$com-topjohnwu-superuser-internal-RootServiceServer()V
    .locals 2

    const-string v0, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->gSzGQˈᵔⁱʽʿﾞˏˎʼʻˉـˋʿʻʽᐧᵔﹶⁱˊDEPLO()Ljava/lang/String;

    move-result-object v0

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->NTZhPˆᵔᵔˆⁱˆˆʼᴵﹶﹳⁱﹳˑˑٴGAGpW()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/topjohnwu/superuser/internal/Utils;->log(Ljava/lang/String;Ljava/lang/Object;)V

    const/4 v0, 0x0

    invoke-direct {p0, v0}, Lcom/topjohnwu/superuser/internal/RootServiceServer;->stopAllService(Z)V

    return-void
.end method

.method synthetic lambda$selfStop$4$com-topjohnwu-superuser-internal-RootServiceServer(Landroid/content/ComponentName;)V
    .locals 4

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p1}, Landroid/content/ComponentName;->getClassName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->LwygVˋˆﹳˋˈﾞʿˋٴـʼᵔˆˊaIvmb()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->nNzWDʻﹶᴵᵔᵔˊᵢʼˆᐧˋˊˆᵢitaGi()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1, v0}, Lcom/topjohnwu/superuser/internal/Utils;->log(Ljava/lang/String;Ljava/lang/Object;)V

    const/4 v0, 0x1

    invoke-direct {p0, p1, v0}, Lcom/topjohnwu/superuser/internal/RootServiceServer;->stopService(Landroid/content/ComponentName;Z)V

    iget-object v0, p0, Lcom/topjohnwu/superuser/internal/RootServiceServer;->client:Landroid/os/Messenger;

    if-eqz v0, :cond_0

    invoke-static {}, Landroid/os/Message;->obtain()Landroid/os/Message;

    move-result-object v2

    const/4 v3, 0x2

    iput v3, v2, Landroid/os/Message;->what:I

    iput-object p1, v2, Landroid/os/Message;->obj:Ljava/lang/Object;

    :try_start_0
    invoke-virtual {v0, v2}, Landroid/os/Messenger;->send(Landroid/os/Message;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    invoke-static {v1, p1}, Lcom/topjohnwu/superuser/internal/Utils;->err(Ljava/lang/String;Ljava/lang/Throwable;)V

    :cond_0
    :goto_0
    return-void
.end method

.method synthetic lambda$stop$2$com-topjohnwu-superuser-internal-RootServiceServer(Landroid/content/ComponentName;)V
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p1}, Landroid/content/ComponentName;->getClassName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->aBXPEˏˆﾞˆˈˋˈᵢRlwuz()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->gbtKYˆˎʻﾞᴵʾˉˉˎʿʻˉˎʼhwJYg()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1, v0}, Lcom/topjohnwu/superuser/internal/Utils;->log(Ljava/lang/String;Ljava/lang/Object;)V

    const/4 v0, 0x1

    invoke-direct {p0, p1, v0}, Lcom/topjohnwu/superuser/internal/RootServiceServer;->stopService(Landroid/content/ComponentName;Z)V

    iget-object p1, p0, Lcom/topjohnwu/superuser/internal/RootServiceServer;->client:Landroid/os/Messenger;

    if-nez p1, :cond_0

    invoke-virtual {p0}, Lcom/topjohnwu/superuser/internal/RootServiceServer;->broadcast()V

    :cond_0
    return-void
.end method

.method synthetic lambda$unbind$1$com-topjohnwu-superuser-internal-RootServiceServer(Landroid/content/ComponentName;)V
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p1}, Landroid/content/ComponentName;->getClassName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->ZQiszᵎˊᵢˊʽˉⁱﾞᴵᵔTXUAa()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->ZUeFFˆٴʻـˋﹳˉˋˉˏʿﹳˆﹳᴵˎʽʾʾˉⁱᵔˉᐧˉmWbLh()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1, v0}, Lcom/topjohnwu/superuser/internal/Utils;->log(Ljava/lang/String;Ljava/lang/Object;)V

    const/4 v0, 0x0

    invoke-direct {p0, p1, v0}, Lcom/topjohnwu/superuser/internal/RootServiceServer;->stopService(Landroid/content/ComponentName;Z)V

    return-void
.end method

.method public register(Lcom/topjohnwu/superuser/ipc/RootService;)V
    .locals 2

    new-instance v0, Lcom/topjohnwu/superuser/internal/RootServiceServer$ServiceContainer;

    invoke-direct {v0}, Lcom/topjohnwu/superuser/internal/RootServiceServer$ServiceContainer;-><init>()V

    iput-object p1, v0, Lcom/topjohnwu/superuser/internal/RootServiceServer$ServiceContainer;->service:Lcom/topjohnwu/superuser/ipc/RootService;

    iget-object v1, p0, Lcom/topjohnwu/superuser/internal/RootServiceServer;->activeServices:Ljava/util/Map;

    invoke-virtual {p1}, Lcom/topjohnwu/superuser/ipc/RootService;->getComponentName()Landroid/content/ComponentName;

    move-result-object p1

    invoke-interface {v1, p1, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public selfStop(Landroid/content/ComponentName;)V
    .locals 1

    new-instance v0, Lcom/topjohnwu/superuser/internal/RootServiceServer$$ExternalSyntheticLambda1;

    invoke-direct {v0, p0, p1}, Lcom/topjohnwu/superuser/internal/RootServiceServer$$ExternalSyntheticLambda1;-><init>(Lcom/topjohnwu/superuser/internal/RootServiceServer;Landroid/content/ComponentName;)V

    invoke-static {v0}, Lcom/topjohnwu/superuser/internal/UiThreadHandler;->run(Ljava/lang/Runnable;)V

    return-void
.end method

.method public setAction(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/topjohnwu/superuser/internal/RootServiceServer;->mAction:Ljava/lang/String;

    return-void
.end method

.method public stop(Landroid/content/ComponentName;)V
    .locals 1

    new-instance v0, Lcom/topjohnwu/superuser/internal/RootServiceServer$$ExternalSyntheticLambda2;

    invoke-direct {v0, p0, p1}, Lcom/topjohnwu/superuser/internal/RootServiceServer$$ExternalSyntheticLambda2;-><init>(Lcom/topjohnwu/superuser/internal/RootServiceServer;Landroid/content/ComponentName;)V

    invoke-static {v0}, Lcom/topjohnwu/superuser/internal/UiThreadHandler;->run(Ljava/lang/Runnable;)V

    return-void
.end method

.method public unbind(Landroid/content/ComponentName;)V
    .locals 1

    new-instance v0, Lcom/topjohnwu/superuser/internal/RootServiceServer$$ExternalSyntheticLambda3;

    invoke-direct {v0, p0, p1}, Lcom/topjohnwu/superuser/internal/RootServiceServer$$ExternalSyntheticLambda3;-><init>(Lcom/topjohnwu/superuser/internal/RootServiceServer;Landroid/content/ComponentName;)V

    invoke-static {v0}, Lcom/topjohnwu/superuser/internal/UiThreadHandler;->run(Ljava/lang/Runnable;)V

    return-void
.end method
