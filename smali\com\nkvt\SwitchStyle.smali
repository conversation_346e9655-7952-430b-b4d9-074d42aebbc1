.class public Lcom/nkvt/SwitchStyle;
.super Landroid/view/View;
.source "SwitchStyle.java"

# interfaces
.implements Landroid/widget/Checkable;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/nkvt/SwitchStyle$ViewState;,
        Lcom/nkvt/SwitchStyle$OnCheckedChangeListener;
    }
.end annotation


# static fields
.field private static final DEFAULT_HEIGHT:I

.field private static final DEFAULT_WIDTH:I


# instance fields
.field private final ANIMATE_STATE_DRAGING:I

.field private final ANIMATE_STATE_NONE:I

.field private final ANIMATE_STATE_PENDING_DRAG:I

.field private final ANIMATE_STATE_PENDING_RESET:I

.field private final ANIMATE_STATE_PENDING_SETTLE:I

.field private final ANIMATE_STATE_SWITCH:I

.field private afterState:Lcom/nkvt/SwitchStyle$ViewState;

.field private animateState:I

.field private animatorListener:Landroid/animation/Animator$AnimatorListener;

.field private animatorUpdateListener:Landroid/animation/ValueAnimator$AnimatorUpdateListener;

.field private final argbEvaluator:Landroid/animation/ArgbEvaluator;

.field private background:I

.field private beforeState:Lcom/nkvt/SwitchStyle$ViewState;

.field private borderWidth:I

.field private bottom:F

.field private buttonEdgeFrame:Z

.field private buttonMaxX:F

.field private buttonMinX:F

.field private buttonPaint:Landroid/graphics/Paint;

.field private buttonRadius:F

.field private centerX:F

.field private centerY:F

.field private checkLineColor:I

.field private checkLineLength:F

.field private checkLineWidth:I

.field private checkedButtonColor:I

.field private checkedColor:I

.field private checkedLineOffsetX:F

.field private checkedLineOffsetY:F

.field private enableEffect:Z

.field private height:F

.field private isChecked:Z

.field private isEventBroadcast:Z

.field private isTouchingDown:Z

.field private isUiInited:Z

.field private left:F

.field private onCheckedChangeListener:Lcom/nkvt/SwitchStyle$OnCheckedChangeListener;

.field private paint:Landroid/graphics/Paint;

.field private postPendingDrag:Ljava/lang/Runnable;

.field private rect:Landroid/graphics/RectF;

.field private right:F

.field private shadowColor:I

.field private shadowEffect:Z

.field private shadowOffset:I

.field private shadowRadius:I

.field private showIndicator:Z

.field private top:F

.field private touchDownTime:J

.field private uncheckButtonColor:I

.field private uncheckCircleColor:I

.field private uncheckCircleOffsetX:F

.field private uncheckCircleRadius:F

.field private uncheckCircleWidth:I

.field private uncheckColor:I

.field private valueAnimator:Landroid/animation/ValueAnimator;

.field private viewRadius:F

.field private viewState:Lcom/nkvt/SwitchStyle$ViewState;

.field private width:F


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 21
    const/high16 v0, 0x42380000    # 46.0f

    invoke-static {v0}, Lcom/nkvt/SwitchStyle;->dp2pxInt(F)I

    move-result v0

    sput v0, Lcom/nkvt/SwitchStyle;->DEFAULT_WIDTH:I

    .line 22
    const/high16 v0, 0x41c80000    # 25.0f

    invoke-static {v0}, Lcom/nkvt/SwitchStyle;->dp2pxInt(F)I

    move-result v0

    sput v0, Lcom/nkvt/SwitchStyle;->DEFAULT_HEIGHT:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 3
    .param p1, "context"    # Landroid/content/Context;

    .line 41
    invoke-direct {p0, p1}, Landroid/view/View;-><init>(Landroid/content/Context;)V

    .line 33
    const/4 v0, 0x0

    iput v0, p0, Lcom/nkvt/SwitchStyle;->ANIMATE_STATE_NONE:I

    .line 34
    const/4 v1, 0x1

    iput v1, p0, Lcom/nkvt/SwitchStyle;->ANIMATE_STATE_PENDING_DRAG:I

    .line 35
    const/4 v2, 0x2

    iput v2, p0, Lcom/nkvt/SwitchStyle;->ANIMATE_STATE_DRAGING:I

    .line 36
    const/4 v2, 0x3

    iput v2, p0, Lcom/nkvt/SwitchStyle;->ANIMATE_STATE_PENDING_RESET:I

    .line 37
    const/4 v2, 0x4

    iput v2, p0, Lcom/nkvt/SwitchStyle;->ANIMATE_STATE_PENDING_SETTLE:I

    .line 38
    const/4 v2, 0x5

    iput v2, p0, Lcom/nkvt/SwitchStyle;->ANIMATE_STATE_SWITCH:I

    .line 918
    iput-boolean v1, p0, Lcom/nkvt/SwitchStyle;->buttonEdgeFrame:Z

    .line 945
    new-instance v1, Landroid/graphics/RectF;

    invoke-direct {v1}, Landroid/graphics/RectF;-><init>()V

    iput-object v1, p0, Lcom/nkvt/SwitchStyle;->rect:Landroid/graphics/RectF;

    .line 949
    iput v0, p0, Lcom/nkvt/SwitchStyle;->animateState:I

    .line 956
    new-instance v1, Landroid/animation/ArgbEvaluator;

    invoke-direct {v1}, Landroid/animation/ArgbEvaluator;-><init>()V

    iput-object v1, p0, Lcom/nkvt/SwitchStyle;->argbEvaluator:Landroid/animation/ArgbEvaluator;

    .line 978
    iput-boolean v0, p0, Lcom/nkvt/SwitchStyle;->isTouchingDown:Z

    .line 982
    iput-boolean v0, p0, Lcom/nkvt/SwitchStyle;->isUiInited:Z

    .line 986
    iput-boolean v0, p0, Lcom/nkvt/SwitchStyle;->isEventBroadcast:Z

    .line 995
    new-instance v0, Lcom/nkvt/SwitchStyle$1;

    invoke-direct {v0, p0}, Lcom/nkvt/SwitchStyle$1;-><init>(Lcom/nkvt/SwitchStyle;)V

    iput-object v0, p0, Lcom/nkvt/SwitchStyle;->postPendingDrag:Ljava/lang/Runnable;

    .line 1004
    new-instance v0, Lcom/nkvt/SwitchStyle$2;

    invoke-direct {v0, p0}, Lcom/nkvt/SwitchStyle$2;-><init>(Lcom/nkvt/SwitchStyle;)V

    iput-object v0, p0, Lcom/nkvt/SwitchStyle;->animatorUpdateListener:Landroid/animation/ValueAnimator$AnimatorUpdateListener;

    .line 1068
    new-instance v0, Lcom/nkvt/SwitchStyle$3;

    invoke-direct {v0, p0}, Lcom/nkvt/SwitchStyle$3;-><init>(Lcom/nkvt/SwitchStyle;)V

    iput-object v0, p0, Lcom/nkvt/SwitchStyle;->animatorListener:Landroid/animation/Animator$AnimatorListener;

    .line 42
    const/4 v0, 0x0

    invoke-direct {p0, p1, v0}, Lcom/nkvt/SwitchStyle;->init(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 43
    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 3
    .param p1, "context"    # Landroid/content/Context;
    .param p2, "attrs"    # Landroid/util/AttributeSet;

    .line 46
    invoke-direct {p0, p1, p2}, Landroid/view/View;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 33
    const/4 v0, 0x0

    iput v0, p0, Lcom/nkvt/SwitchStyle;->ANIMATE_STATE_NONE:I

    .line 34
    const/4 v1, 0x1

    iput v1, p0, Lcom/nkvt/SwitchStyle;->ANIMATE_STATE_PENDING_DRAG:I

    .line 35
    const/4 v2, 0x2

    iput v2, p0, Lcom/nkvt/SwitchStyle;->ANIMATE_STATE_DRAGING:I

    .line 36
    const/4 v2, 0x3

    iput v2, p0, Lcom/nkvt/SwitchStyle;->ANIMATE_STATE_PENDING_RESET:I

    .line 37
    const/4 v2, 0x4

    iput v2, p0, Lcom/nkvt/SwitchStyle;->ANIMATE_STATE_PENDING_SETTLE:I

    .line 38
    const/4 v2, 0x5

    iput v2, p0, Lcom/nkvt/SwitchStyle;->ANIMATE_STATE_SWITCH:I

    .line 918
    iput-boolean v1, p0, Lcom/nkvt/SwitchStyle;->buttonEdgeFrame:Z

    .line 945
    new-instance v1, Landroid/graphics/RectF;

    invoke-direct {v1}, Landroid/graphics/RectF;-><init>()V

    iput-object v1, p0, Lcom/nkvt/SwitchStyle;->rect:Landroid/graphics/RectF;

    .line 949
    iput v0, p0, Lcom/nkvt/SwitchStyle;->animateState:I

    .line 956
    new-instance v1, Landroid/animation/ArgbEvaluator;

    invoke-direct {v1}, Landroid/animation/ArgbEvaluator;-><init>()V

    iput-object v1, p0, Lcom/nkvt/SwitchStyle;->argbEvaluator:Landroid/animation/ArgbEvaluator;

    .line 978
    iput-boolean v0, p0, Lcom/nkvt/SwitchStyle;->isTouchingDown:Z

    .line 982
    iput-boolean v0, p0, Lcom/nkvt/SwitchStyle;->isUiInited:Z

    .line 986
    iput-boolean v0, p0, Lcom/nkvt/SwitchStyle;->isEventBroadcast:Z

    .line 995
    new-instance v0, Lcom/nkvt/SwitchStyle$1;

    invoke-direct {v0, p0}, Lcom/nkvt/SwitchStyle$1;-><init>(Lcom/nkvt/SwitchStyle;)V

    iput-object v0, p0, Lcom/nkvt/SwitchStyle;->postPendingDrag:Ljava/lang/Runnable;

    .line 1004
    new-instance v0, Lcom/nkvt/SwitchStyle$2;

    invoke-direct {v0, p0}, Lcom/nkvt/SwitchStyle$2;-><init>(Lcom/nkvt/SwitchStyle;)V

    iput-object v0, p0, Lcom/nkvt/SwitchStyle;->animatorUpdateListener:Landroid/animation/ValueAnimator$AnimatorUpdateListener;

    .line 1068
    new-instance v0, Lcom/nkvt/SwitchStyle$3;

    invoke-direct {v0, p0}, Lcom/nkvt/SwitchStyle$3;-><init>(Lcom/nkvt/SwitchStyle;)V

    iput-object v0, p0, Lcom/nkvt/SwitchStyle;->animatorListener:Landroid/animation/Animator$AnimatorListener;

    .line 47
    invoke-direct {p0, p1, p2}, Lcom/nkvt/SwitchStyle;->init(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 48
    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 3
    .param p1, "context"    # Landroid/content/Context;
    .param p2, "attrs"    # Landroid/util/AttributeSet;
    .param p3, "defStyleAttr"    # I

    .line 51
    invoke-direct {p0, p1, p2, p3}, Landroid/view/View;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 33
    const/4 v0, 0x0

    iput v0, p0, Lcom/nkvt/SwitchStyle;->ANIMATE_STATE_NONE:I

    .line 34
    const/4 v1, 0x1

    iput v1, p0, Lcom/nkvt/SwitchStyle;->ANIMATE_STATE_PENDING_DRAG:I

    .line 35
    const/4 v2, 0x2

    iput v2, p0, Lcom/nkvt/SwitchStyle;->ANIMATE_STATE_DRAGING:I

    .line 36
    const/4 v2, 0x3

    iput v2, p0, Lcom/nkvt/SwitchStyle;->ANIMATE_STATE_PENDING_RESET:I

    .line 37
    const/4 v2, 0x4

    iput v2, p0, Lcom/nkvt/SwitchStyle;->ANIMATE_STATE_PENDING_SETTLE:I

    .line 38
    const/4 v2, 0x5

    iput v2, p0, Lcom/nkvt/SwitchStyle;->ANIMATE_STATE_SWITCH:I

    .line 918
    iput-boolean v1, p0, Lcom/nkvt/SwitchStyle;->buttonEdgeFrame:Z

    .line 945
    new-instance v1, Landroid/graphics/RectF;

    invoke-direct {v1}, Landroid/graphics/RectF;-><init>()V

    iput-object v1, p0, Lcom/nkvt/SwitchStyle;->rect:Landroid/graphics/RectF;

    .line 949
    iput v0, p0, Lcom/nkvt/SwitchStyle;->animateState:I

    .line 956
    new-instance v1, Landroid/animation/ArgbEvaluator;

    invoke-direct {v1}, Landroid/animation/ArgbEvaluator;-><init>()V

    iput-object v1, p0, Lcom/nkvt/SwitchStyle;->argbEvaluator:Landroid/animation/ArgbEvaluator;

    .line 978
    iput-boolean v0, p0, Lcom/nkvt/SwitchStyle;->isTouchingDown:Z

    .line 982
    iput-boolean v0, p0, Lcom/nkvt/SwitchStyle;->isUiInited:Z

    .line 986
    iput-boolean v0, p0, Lcom/nkvt/SwitchStyle;->isEventBroadcast:Z

    .line 995
    new-instance v0, Lcom/nkvt/SwitchStyle$1;

    invoke-direct {v0, p0}, Lcom/nkvt/SwitchStyle$1;-><init>(Lcom/nkvt/SwitchStyle;)V

    iput-object v0, p0, Lcom/nkvt/SwitchStyle;->postPendingDrag:Ljava/lang/Runnable;

    .line 1004
    new-instance v0, Lcom/nkvt/SwitchStyle$2;

    invoke-direct {v0, p0}, Lcom/nkvt/SwitchStyle$2;-><init>(Lcom/nkvt/SwitchStyle;)V

    iput-object v0, p0, Lcom/nkvt/SwitchStyle;->animatorUpdateListener:Landroid/animation/ValueAnimator$AnimatorUpdateListener;

    .line 1068
    new-instance v0, Lcom/nkvt/SwitchStyle$3;

    invoke-direct {v0, p0}, Lcom/nkvt/SwitchStyle$3;-><init>(Lcom/nkvt/SwitchStyle;)V

    iput-object v0, p0, Lcom/nkvt/SwitchStyle;->animatorListener:Landroid/animation/Animator$AnimatorListener;

    .line 52
    invoke-direct {p0, p1, p2}, Lcom/nkvt/SwitchStyle;->init(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 53
    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;II)V
    .locals 3
    .param p1, "context"    # Landroid/content/Context;
    .param p2, "attrs"    # Landroid/util/AttributeSet;
    .param p3, "defStyleAttr"    # I
    .param p4, "defStyleRes"    # I

    .line 57
    invoke-direct {p0, p1, p2, p3, p4}, Landroid/view/View;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;II)V

    .line 33
    const/4 v0, 0x0

    iput v0, p0, Lcom/nkvt/SwitchStyle;->ANIMATE_STATE_NONE:I

    .line 34
    const/4 v1, 0x1

    iput v1, p0, Lcom/nkvt/SwitchStyle;->ANIMATE_STATE_PENDING_DRAG:I

    .line 35
    const/4 v2, 0x2

    iput v2, p0, Lcom/nkvt/SwitchStyle;->ANIMATE_STATE_DRAGING:I

    .line 36
    const/4 v2, 0x3

    iput v2, p0, Lcom/nkvt/SwitchStyle;->ANIMATE_STATE_PENDING_RESET:I

    .line 37
    const/4 v2, 0x4

    iput v2, p0, Lcom/nkvt/SwitchStyle;->ANIMATE_STATE_PENDING_SETTLE:I

    .line 38
    const/4 v2, 0x5

    iput v2, p0, Lcom/nkvt/SwitchStyle;->ANIMATE_STATE_SWITCH:I

    .line 918
    iput-boolean v1, p0, Lcom/nkvt/SwitchStyle;->buttonEdgeFrame:Z

    .line 945
    new-instance v1, Landroid/graphics/RectF;

    invoke-direct {v1}, Landroid/graphics/RectF;-><init>()V

    iput-object v1, p0, Lcom/nkvt/SwitchStyle;->rect:Landroid/graphics/RectF;

    .line 949
    iput v0, p0, Lcom/nkvt/SwitchStyle;->animateState:I

    .line 956
    new-instance v1, Landroid/animation/ArgbEvaluator;

    invoke-direct {v1}, Landroid/animation/ArgbEvaluator;-><init>()V

    iput-object v1, p0, Lcom/nkvt/SwitchStyle;->argbEvaluator:Landroid/animation/ArgbEvaluator;

    .line 978
    iput-boolean v0, p0, Lcom/nkvt/SwitchStyle;->isTouchingDown:Z

    .line 982
    iput-boolean v0, p0, Lcom/nkvt/SwitchStyle;->isUiInited:Z

    .line 986
    iput-boolean v0, p0, Lcom/nkvt/SwitchStyle;->isEventBroadcast:Z

    .line 995
    new-instance v0, Lcom/nkvt/SwitchStyle$1;

    invoke-direct {v0, p0}, Lcom/nkvt/SwitchStyle$1;-><init>(Lcom/nkvt/SwitchStyle;)V

    iput-object v0, p0, Lcom/nkvt/SwitchStyle;->postPendingDrag:Ljava/lang/Runnable;

    .line 1004
    new-instance v0, Lcom/nkvt/SwitchStyle$2;

    invoke-direct {v0, p0}, Lcom/nkvt/SwitchStyle$2;-><init>(Lcom/nkvt/SwitchStyle;)V

    iput-object v0, p0, Lcom/nkvt/SwitchStyle;->animatorUpdateListener:Landroid/animation/ValueAnimator$AnimatorUpdateListener;

    .line 1068
    new-instance v0, Lcom/nkvt/SwitchStyle$3;

    invoke-direct {v0, p0}, Lcom/nkvt/SwitchStyle$3;-><init>(Lcom/nkvt/SwitchStyle;)V

    iput-object v0, p0, Lcom/nkvt/SwitchStyle;->animatorListener:Landroid/animation/Animator$AnimatorListener;

    .line 58
    invoke-direct {p0, p1, p2}, Lcom/nkvt/SwitchStyle;->init(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 59
    return-void
.end method

.method static synthetic access$100(Lcom/nkvt/SwitchStyle;)Z
    .locals 1
    .param p0, "x0"    # Lcom/nkvt/SwitchStyle;

    .line 20
    invoke-direct {p0}, Lcom/nkvt/SwitchStyle;->isInAnimating()Z

    move-result v0

    return v0
.end method

.method static synthetic access$1000(Lcom/nkvt/SwitchStyle;)I
    .locals 1
    .param p0, "x0"    # Lcom/nkvt/SwitchStyle;

    .line 20
    iget v0, p0, Lcom/nkvt/SwitchStyle;->uncheckColor:I

    return v0
.end method

.method static synthetic access$1100(Lcom/nkvt/SwitchStyle;)I
    .locals 1
    .param p0, "x0"    # Lcom/nkvt/SwitchStyle;

    .line 20
    iget v0, p0, Lcom/nkvt/SwitchStyle;->checkedColor:I

    return v0
.end method

.method static synthetic access$1200(Lcom/nkvt/SwitchStyle;)F
    .locals 1
    .param p0, "x0"    # Lcom/nkvt/SwitchStyle;

    .line 20
    iget v0, p0, Lcom/nkvt/SwitchStyle;->viewRadius:F

    return v0
.end method

.method static synthetic access$1300(Lcom/nkvt/SwitchStyle;)I
    .locals 1
    .param p0, "x0"    # Lcom/nkvt/SwitchStyle;

    .line 20
    iget v0, p0, Lcom/nkvt/SwitchStyle;->checkLineColor:I

    return v0
.end method

.method static synthetic access$1400(Lcom/nkvt/SwitchStyle;)V
    .locals 0
    .param p0, "x0"    # Lcom/nkvt/SwitchStyle;

    .line 20
    invoke-direct {p0}, Lcom/nkvt/SwitchStyle;->broadcastEvent()V

    return-void
.end method

.method static synthetic access$1500(Lcom/nkvt/SwitchStyle;)Z
    .locals 1
    .param p0, "x0"    # Lcom/nkvt/SwitchStyle;

    .line 20
    iget-boolean v0, p0, Lcom/nkvt/SwitchStyle;->isChecked:Z

    return v0
.end method

.method static synthetic access$1502(Lcom/nkvt/SwitchStyle;Z)Z
    .locals 0
    .param p0, "x0"    # Lcom/nkvt/SwitchStyle;
    .param p1, "x1"    # Z

    .line 20
    iput-boolean p1, p0, Lcom/nkvt/SwitchStyle;->isChecked:Z

    return p1
.end method

.method static synthetic access$200(Lcom/nkvt/SwitchStyle;)V
    .locals 0
    .param p0, "x0"    # Lcom/nkvt/SwitchStyle;

    .line 20
    invoke-direct {p0}, Lcom/nkvt/SwitchStyle;->pendingDragState()V

    return-void
.end method

.method static synthetic access$300(Lcom/nkvt/SwitchStyle;)I
    .locals 1
    .param p0, "x0"    # Lcom/nkvt/SwitchStyle;

    .line 20
    iget v0, p0, Lcom/nkvt/SwitchStyle;->animateState:I

    return v0
.end method

.method static synthetic access$302(Lcom/nkvt/SwitchStyle;I)I
    .locals 0
    .param p0, "x0"    # Lcom/nkvt/SwitchStyle;
    .param p1, "x1"    # I

    .line 20
    iput p1, p0, Lcom/nkvt/SwitchStyle;->animateState:I

    return p1
.end method

.method static synthetic access$400(Lcom/nkvt/SwitchStyle;)Lcom/nkvt/SwitchStyle$ViewState;
    .locals 1
    .param p0, "x0"    # Lcom/nkvt/SwitchStyle;

    .line 20
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->viewState:Lcom/nkvt/SwitchStyle$ViewState;

    return-object v0
.end method

.method static synthetic access$500(Lcom/nkvt/SwitchStyle;)Lcom/nkvt/SwitchStyle$ViewState;
    .locals 1
    .param p0, "x0"    # Lcom/nkvt/SwitchStyle;

    .line 20
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->beforeState:Lcom/nkvt/SwitchStyle$ViewState;

    return-object v0
.end method

.method static synthetic access$600(Lcom/nkvt/SwitchStyle;)Lcom/nkvt/SwitchStyle$ViewState;
    .locals 1
    .param p0, "x0"    # Lcom/nkvt/SwitchStyle;

    .line 20
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->afterState:Lcom/nkvt/SwitchStyle$ViewState;

    return-object v0
.end method

.method static synthetic access$700(Lcom/nkvt/SwitchStyle;)Landroid/animation/ArgbEvaluator;
    .locals 1
    .param p0, "x0"    # Lcom/nkvt/SwitchStyle;

    .line 20
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->argbEvaluator:Landroid/animation/ArgbEvaluator;

    return-object v0
.end method

.method static synthetic access$800(Lcom/nkvt/SwitchStyle;)F
    .locals 1
    .param p0, "x0"    # Lcom/nkvt/SwitchStyle;

    .line 20
    iget v0, p0, Lcom/nkvt/SwitchStyle;->buttonMinX:F

    return v0
.end method

.method static synthetic access$900(Lcom/nkvt/SwitchStyle;)F
    .locals 1
    .param p0, "x0"    # Lcom/nkvt/SwitchStyle;

    .line 20
    iget v0, p0, Lcom/nkvt/SwitchStyle;->buttonMaxX:F

    return v0
.end method

.method private broadcastEvent()V
    .locals 2

    .line 574
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->onCheckedChangeListener:Lcom/nkvt/SwitchStyle$OnCheckedChangeListener;

    if-eqz v0, :cond_0

    .line 575
    const/4 v1, 0x1

    iput-boolean v1, p0, Lcom/nkvt/SwitchStyle;->isEventBroadcast:Z

    .line 576
    invoke-virtual {p0}, Lcom/nkvt/SwitchStyle;->isChecked()Z

    move-result v1

    invoke-interface {v0, p0, v1}, Lcom/nkvt/SwitchStyle$OnCheckedChangeListener;->onCheckedChanged(Lcom/nkvt/SwitchStyle;Z)V

    .line 578
    :cond_0
    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/nkvt/SwitchStyle;->isEventBroadcast:Z

    .line 579
    return-void
.end method

.method private static dp2px(F)F
    .locals 3
    .param p0, "dp"    # F

    .line 768
    invoke-static {}, Landroid/content/res/Resources;->getSystem()Landroid/content/res/Resources;

    move-result-object v0

    .line 769
    .local v0, "r":Landroid/content/res/Resources;
    invoke-virtual {v0}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v1

    const/4 v2, 0x1

    invoke-static {v2, p0, v1}, Landroid/util/TypedValue;->applyDimension(IFLandroid/util/DisplayMetrics;)F

    move-result v1

    return v1
.end method

.method private static dp2pxInt(F)I
    .locals 1
    .param p0, "dp"    # F

    .line 773
    invoke-static {p0}, Lcom/nkvt/SwitchStyle;->dp2px(F)F

    move-result v0

    float-to-int v0, v0

    return v0
.end method

.method private drawArc(Landroid/graphics/Canvas;FFFFFFLandroid/graphics/Paint;)V
    .locals 12
    .param p1, "canvas"    # Landroid/graphics/Canvas;
    .param p2, "left"    # F
    .param p3, "top"    # F
    .param p4, "right"    # F
    .param p5, "bottom"    # F
    .param p6, "startAngle"    # F
    .param p7, "sweepAngle"    # F
    .param p8, "paint"    # Landroid/graphics/Paint;

    .line 475
    move-object v0, p0

    sget v1, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v2, 0x15

    if-lt v1, v2, :cond_0

    .line 476
    const/4 v10, 0x1

    move-object v3, p1

    move v4, p2

    move v5, p3

    move/from16 v6, p4

    move/from16 v7, p5

    move/from16 v8, p6

    move/from16 v9, p7

    move-object/from16 v11, p8

    invoke-virtual/range {v3 .. v11}, Landroid/graphics/Canvas;->drawArc(FFFFFFZLandroid/graphics/Paint;)V

    move v2, p2

    move v3, p3

    move/from16 v4, p4

    move/from16 v5, p5

    goto :goto_0

    .line 479
    :cond_0
    iget-object v1, v0, Lcom/nkvt/SwitchStyle;->rect:Landroid/graphics/RectF;

    move v2, p2

    move v3, p3

    move/from16 v4, p4

    move/from16 v5, p5

    invoke-virtual {v1, p2, p3, v4, v5}, Landroid/graphics/RectF;->set(FFFF)V

    .line 480
    iget-object v7, v0, Lcom/nkvt/SwitchStyle;->rect:Landroid/graphics/RectF;

    const/4 v10, 0x1

    move-object v6, p1

    move/from16 v8, p6

    move/from16 v9, p7

    move-object/from16 v11, p8

    invoke-virtual/range {v6 .. v11}, Landroid/graphics/Canvas;->drawArc(Landroid/graphics/RectF;FFZLandroid/graphics/Paint;)V

    .line 483
    :goto_0
    return-void
.end method

.method private drawButton(Landroid/graphics/Canvas;FF)V
    .locals 2
    .param p1, "canvas"    # Landroid/graphics/Canvas;
    .param p2, "x"    # F
    .param p3, "y"    # F

    .line 516
    iget v0, p0, Lcom/nkvt/SwitchStyle;->buttonRadius:F

    iget-object v1, p0, Lcom/nkvt/SwitchStyle;->buttonPaint:Landroid/graphics/Paint;

    invoke-virtual {p1, p2, p3, v0, v1}, Landroid/graphics/Canvas;->drawCircle(FFFLandroid/graphics/Paint;)V

    .line 518
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->paint:Landroid/graphics/Paint;

    sget-object v1, Landroid/graphics/Paint$Style;->STROKE:Landroid/graphics/Paint$Style;

    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setStyle(Landroid/graphics/Paint$Style;)V

    .line 519
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->paint:Landroid/graphics/Paint;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setStrokeWidth(F)V

    .line 520
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->paint:Landroid/graphics/Paint;

    const v1, -0x222223

    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setColor(I)V

    .line 521
    iget-boolean v0, p0, Lcom/nkvt/SwitchStyle;->buttonEdgeFrame:Z

    if-eqz v0, :cond_0

    .line 522
    iget v0, p0, Lcom/nkvt/SwitchStyle;->buttonRadius:F

    iget-object v1, p0, Lcom/nkvt/SwitchStyle;->paint:Landroid/graphics/Paint;

    invoke-virtual {p1, p2, p3, v0, v1}, Landroid/graphics/Canvas;->drawCircle(FFFLandroid/graphics/Paint;)V

    .line 524
    :cond_0
    return-void
.end method

.method private drawRoundRect(Landroid/graphics/Canvas;FFFFFLandroid/graphics/Paint;)V
    .locals 8
    .param p1, "canvas"    # Landroid/graphics/Canvas;
    .param p2, "left"    # F
    .param p3, "top"    # F
    .param p4, "right"    # F
    .param p5, "bottom"    # F
    .param p6, "backgroundRadius"    # F
    .param p7, "paint"    # Landroid/graphics/Paint;

    .line 499
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x15

    if-lt v0, v1, :cond_0

    .line 500
    move-object v0, p1

    move v1, p2

    move v2, p3

    move v3, p4

    move v4, p5

    move v5, p6

    move v6, p6

    move-object v7, p7

    invoke-virtual/range {v0 .. v7}, Landroid/graphics/Canvas;->drawRoundRect(FFFFFFLandroid/graphics/Paint;)V

    goto :goto_0

    .line 503
    :cond_0
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->rect:Landroid/graphics/RectF;

    invoke-virtual {v0, p2, p3, p4, p5}, Landroid/graphics/RectF;->set(FFFF)V

    .line 504
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->rect:Landroid/graphics/RectF;

    invoke-virtual {p1, v0, p6, p6, p7}, Landroid/graphics/Canvas;->drawRoundRect(Landroid/graphics/RectF;FFLandroid/graphics/Paint;)V

    .line 507
    :goto_0
    return-void
.end method

.method private drawUncheckIndicator(Landroid/graphics/Canvas;)V
    .locals 8
    .param p1, "canvas"    # Landroid/graphics/Canvas;

    .line 429
    iget v2, p0, Lcom/nkvt/SwitchStyle;->uncheckCircleColor:I

    iget v0, p0, Lcom/nkvt/SwitchStyle;->uncheckCircleWidth:I

    int-to-float v3, v0

    iget v0, p0, Lcom/nkvt/SwitchStyle;->right:F

    iget v1, p0, Lcom/nkvt/SwitchStyle;->uncheckCircleOffsetX:F

    sub-float v4, v0, v1

    iget v5, p0, Lcom/nkvt/SwitchStyle;->centerY:F

    iget v6, p0, Lcom/nkvt/SwitchStyle;->uncheckCircleRadius:F

    iget-object v7, p0, Lcom/nkvt/SwitchStyle;->paint:Landroid/graphics/Paint;

    move-object v0, p0

    move-object v1, p1

    invoke-virtual/range {v0 .. v7}, Lcom/nkvt/SwitchStyle;->drawUncheckIndicator(Landroid/graphics/Canvas;IFFFFLandroid/graphics/Paint;)V

    .line 435
    return-void
.end method

.method private init(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 10
    .param p1, "context"    # Landroid/content/Context;
    .param p2, "attrs"    # Landroid/util/AttributeSet;

    .line 138
    const/4 v0, 0x0

    .line 141
    .local v0, "typedArray":Landroid/content/res/TypedArray;
    const/4 v1, 0x0

    const/4 v2, 0x1

    invoke-static {v0, v1, v2}, Lcom/nkvt/SwitchStyle;->optBoolean(Landroid/content/res/TypedArray;IZ)Z

    move-result v3

    iput-boolean v3, p0, Lcom/nkvt/SwitchStyle;->shadowEffect:Z

    .line 145
    const v3, 0xaaaaaa

    invoke-static {v0, v1, v3}, Lcom/nkvt/SwitchStyle;->optColor(Landroid/content/res/TypedArray;II)I

    move-result v3

    iput v3, p0, Lcom/nkvt/SwitchStyle;->uncheckCircleColor:I

    .line 149
    nop

    .line 151
    const/high16 v3, 0x3fc00000    # 1.5f

    invoke-static {v3}, Lcom/nkvt/SwitchStyle;->dp2pxInt(F)I

    move-result v4

    .line 149
    invoke-static {v0, v1, v4}, Lcom/nkvt/SwitchStyle;->optPixelSize(Landroid/content/res/TypedArray;II)I

    move-result v4

    iput v4, p0, Lcom/nkvt/SwitchStyle;->uncheckCircleWidth:I

    .line 153
    const/high16 v4, 0x41200000    # 10.0f

    invoke-static {v4}, Lcom/nkvt/SwitchStyle;->dp2px(F)F

    move-result v4

    iput v4, p0, Lcom/nkvt/SwitchStyle;->uncheckCircleOffsetX:F

    .line 155
    nop

    .line 157
    const/high16 v4, 0x40800000    # 4.0f

    invoke-static {v4}, Lcom/nkvt/SwitchStyle;->dp2px(F)F

    move-result v4

    .line 155
    invoke-static {v0, v1, v4}, Lcom/nkvt/SwitchStyle;->optPixelSize(Landroid/content/res/TypedArray;IF)F

    move-result v4

    iput v4, p0, Lcom/nkvt/SwitchStyle;->uncheckCircleRadius:F

    .line 159
    const/4 v4, 0x0

    invoke-static {v4}, Lcom/nkvt/SwitchStyle;->dp2px(F)F

    move-result v5

    iput v5, p0, Lcom/nkvt/SwitchStyle;->checkedLineOffsetX:F

    .line 160
    invoke-static {v4}, Lcom/nkvt/SwitchStyle;->dp2px(F)F

    move-result v5

    iput v5, p0, Lcom/nkvt/SwitchStyle;->checkedLineOffsetY:F

    .line 162
    nop

    .line 164
    const/high16 v5, 0x40200000    # 2.5f

    invoke-static {v5}, Lcom/nkvt/SwitchStyle;->dp2pxInt(F)I

    move-result v5

    .line 162
    invoke-static {v0, v1, v5}, Lcom/nkvt/SwitchStyle;->optPixelSize(Landroid/content/res/TypedArray;II)I

    move-result v5

    iput v5, p0, Lcom/nkvt/SwitchStyle;->shadowRadius:I

    .line 166
    nop

    .line 168
    invoke-static {v3}, Lcom/nkvt/SwitchStyle;->dp2pxInt(F)I

    move-result v3

    .line 166
    invoke-static {v0, v1, v3}, Lcom/nkvt/SwitchStyle;->optPixelSize(Landroid/content/res/TypedArray;II)I

    move-result v3

    iput v3, p0, Lcom/nkvt/SwitchStyle;->shadowOffset:I

    .line 170
    const/high16 v3, -0x1000000

    invoke-static {v0, v1, v3}, Lcom/nkvt/SwitchStyle;->optColor(Landroid/content/res/TypedArray;II)I

    move-result v5

    iput v5, p0, Lcom/nkvt/SwitchStyle;->shadowColor:I

    .line 174
    nop

    .line 175
    const-string v5, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->QEpCQˑʽʼˊʿـיʾﹶˈﾞיʻﹳmYHRP()Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v6

    .line 174
    invoke-static {v0, v6, v3}, Lcom/nkvt/SwitchStyle;->optColor(Landroid/content/res/TypedArray;II)I

    move-result v3

    iput v3, p0, Lcom/nkvt/SwitchStyle;->uncheckColor:I

    .line 178
    sget v3, Lcom/nkvt/Menu;->PrimaryColor:I

    sget v6, Lcom/nkvt/Menu;->PrimaryColor:I

    invoke-static {v0, v3, v6}, Lcom/nkvt/SwitchStyle;->optColor(Landroid/content/res/TypedArray;II)I

    move-result v3

    iput v3, p0, Lcom/nkvt/SwitchStyle;->checkedColor:I

    .line 182
    nop

    .line 184
    const/high16 v3, 0x3f800000    # 1.0f

    invoke-static {v3}, Lcom/nkvt/SwitchStyle;->dp2pxInt(F)I

    move-result v3

    .line 182
    invoke-static {v0, v1, v3}, Lcom/nkvt/SwitchStyle;->optPixelSize(Landroid/content/res/TypedArray;II)I

    move-result v3

    iput v3, p0, Lcom/nkvt/SwitchStyle;->borderWidth:I

    .line 186
    invoke-static {v0, v1, v1}, Lcom/nkvt/SwitchStyle;->optColor(Landroid/content/res/TypedArray;II)I

    move-result v3

    iput v3, p0, Lcom/nkvt/SwitchStyle;->checkLineColor:I

    .line 190
    nop

    .line 192
    invoke-static {v4}, Lcom/nkvt/SwitchStyle;->dp2pxInt(F)I

    move-result v3

    .line 190
    invoke-static {v0, v1, v3}, Lcom/nkvt/SwitchStyle;->optPixelSize(Landroid/content/res/TypedArray;II)I

    move-result v3

    iput v3, p0, Lcom/nkvt/SwitchStyle;->checkLineWidth:I

    .line 194
    const/high16 v3, 0x40c00000    # 6.0f

    invoke-static {v3}, Lcom/nkvt/SwitchStyle;->dp2px(F)F

    move-result v3

    iput v3, p0, Lcom/nkvt/SwitchStyle;->checkLineLength:F

    .line 196
    const/4 v3, -0x1

    invoke-static {v0, v1, v3}, Lcom/nkvt/SwitchStyle;->optColor(Landroid/content/res/TypedArray;II)I

    move-result v6

    .line 200
    .local v6, "buttonColor":I
    nop

    .line 201
    invoke-static {v5}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v5

    .line 200
    invoke-static {v0, v5, v6}, Lcom/nkvt/SwitchStyle;->optColor(Landroid/content/res/TypedArray;II)I

    move-result v5

    iput v5, p0, Lcom/nkvt/SwitchStyle;->uncheckButtonColor:I

    .line 204
    sget v5, Lcom/nkvt/Menu;->PrimaryColor:I

    invoke-static {v0, v5, v6}, Lcom/nkvt/SwitchStyle;->optColor(Landroid/content/res/TypedArray;II)I

    move-result v5

    iput v5, p0, Lcom/nkvt/SwitchStyle;->checkedButtonColor:I

    .line 208
    const/16 v5, 0x12c

    invoke-static {v0, v1, v5}, Lcom/nkvt/SwitchStyle;->optInt(Landroid/content/res/TypedArray;II)I

    move-result v5

    .line 212
    .local v5, "effectDuration":I
    invoke-static {v0, v1, v1}, Lcom/nkvt/SwitchStyle;->optBoolean(Landroid/content/res/TypedArray;IZ)Z

    move-result v7

    iput-boolean v7, p0, Lcom/nkvt/SwitchStyle;->isChecked:Z

    .line 216
    invoke-static {v0, v1, v2}, Lcom/nkvt/SwitchStyle;->optBoolean(Landroid/content/res/TypedArray;IZ)Z

    move-result v7

    iput-boolean v7, p0, Lcom/nkvt/SwitchStyle;->showIndicator:Z

    .line 220
    invoke-static {v0, v1, v3}, Lcom/nkvt/SwitchStyle;->optColor(Landroid/content/res/TypedArray;II)I

    move-result v3

    iput v3, p0, Lcom/nkvt/SwitchStyle;->background:I

    .line 224
    invoke-static {v0, v1, v2}, Lcom/nkvt/SwitchStyle;->optBoolean(Landroid/content/res/TypedArray;IZ)Z

    move-result v3

    iput-boolean v3, p0, Lcom/nkvt/SwitchStyle;->enableEffect:Z

    .line 228
    if-eqz v0, :cond_0

    .line 229
    invoke-virtual {v0}, Landroid/content/res/TypedArray;->recycle()V

    .line 233
    :cond_0
    new-instance v3, Landroid/graphics/Paint;

    invoke-direct {v3, v2}, Landroid/graphics/Paint;-><init>(I)V

    iput-object v3, p0, Lcom/nkvt/SwitchStyle;->paint:Landroid/graphics/Paint;

    .line 234
    new-instance v3, Landroid/graphics/Paint;

    invoke-direct {v3, v2}, Landroid/graphics/Paint;-><init>(I)V

    iput-object v3, p0, Lcom/nkvt/SwitchStyle;->buttonPaint:Landroid/graphics/Paint;

    .line 235
    invoke-virtual {v3, v6}, Landroid/graphics/Paint;->setColor(I)V

    .line 237
    iget-boolean v3, p0, Lcom/nkvt/SwitchStyle;->shadowEffect:Z

    if-eqz v3, :cond_1

    .line 238
    iget-object v3, p0, Lcom/nkvt/SwitchStyle;->buttonPaint:Landroid/graphics/Paint;

    iget v7, p0, Lcom/nkvt/SwitchStyle;->shadowRadius:I

    int-to-float v7, v7

    iget v8, p0, Lcom/nkvt/SwitchStyle;->shadowOffset:I

    int-to-float v8, v8

    iget v9, p0, Lcom/nkvt/SwitchStyle;->shadowColor:I

    invoke-virtual {v3, v7, v4, v8, v9}, Landroid/graphics/Paint;->setShadowLayer(FFFI)V

    .line 245
    :cond_1
    new-instance v3, Lcom/nkvt/SwitchStyle$ViewState;

    invoke-direct {v3}, Lcom/nkvt/SwitchStyle$ViewState;-><init>()V

    iput-object v3, p0, Lcom/nkvt/SwitchStyle;->viewState:Lcom/nkvt/SwitchStyle$ViewState;

    .line 246
    new-instance v3, Lcom/nkvt/SwitchStyle$ViewState;

    invoke-direct {v3}, Lcom/nkvt/SwitchStyle$ViewState;-><init>()V

    iput-object v3, p0, Lcom/nkvt/SwitchStyle;->beforeState:Lcom/nkvt/SwitchStyle$ViewState;

    .line 247
    new-instance v3, Lcom/nkvt/SwitchStyle$ViewState;

    invoke-direct {v3}, Lcom/nkvt/SwitchStyle$ViewState;-><init>()V

    iput-object v3, p0, Lcom/nkvt/SwitchStyle;->afterState:Lcom/nkvt/SwitchStyle$ViewState;

    .line 249
    const/4 v3, 0x2

    new-array v3, v3, [F

    fill-array-data v3, :array_0

    invoke-static {v3}, Landroid/animation/ValueAnimator;->ofFloat([F)Landroid/animation/ValueAnimator;

    move-result-object v3

    iput-object v3, p0, Lcom/nkvt/SwitchStyle;->valueAnimator:Landroid/animation/ValueAnimator;

    .line 250
    int-to-long v7, v5

    invoke-virtual {v3, v7, v8}, Landroid/animation/ValueAnimator;->setDuration(J)Landroid/animation/ValueAnimator;

    .line 251
    iget-object v3, p0, Lcom/nkvt/SwitchStyle;->valueAnimator:Landroid/animation/ValueAnimator;

    invoke-virtual {v3, v1}, Landroid/animation/ValueAnimator;->setRepeatCount(I)V

    .line 253
    iget-object v3, p0, Lcom/nkvt/SwitchStyle;->valueAnimator:Landroid/animation/ValueAnimator;

    iget-object v4, p0, Lcom/nkvt/SwitchStyle;->animatorUpdateListener:Landroid/animation/ValueAnimator$AnimatorUpdateListener;

    invoke-virtual {v3, v4}, Landroid/animation/ValueAnimator;->addUpdateListener(Landroid/animation/ValueAnimator$AnimatorUpdateListener;)V

    .line 254
    iget-object v3, p0, Lcom/nkvt/SwitchStyle;->valueAnimator:Landroid/animation/ValueAnimator;

    iget-object v4, p0, Lcom/nkvt/SwitchStyle;->animatorListener:Landroid/animation/Animator$AnimatorListener;

    invoke-virtual {v3, v4}, Landroid/animation/ValueAnimator;->addListener(Landroid/animation/Animator$AnimatorListener;)V

    .line 256
    invoke-super {p0, v2}, Landroid/view/View;->setClickable(Z)V

    .line 257
    invoke-virtual {p0, v1, v1, v1, v1}, Lcom/nkvt/SwitchStyle;->setPadding(IIII)V

    .line 258
    sget v1, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v3, 0xb

    if-lt v1, v3, :cond_2

    .line 259
    const/4 v1, 0x0

    invoke-virtual {p0, v2, v1}, Lcom/nkvt/SwitchStyle;->setLayerType(ILandroid/graphics/Paint;)V

    .line 261
    :cond_2
    return-void

    :array_0
    .array-data 4
        0x0
        0x3f800000    # 1.0f
    .end array-data
.end method

.method private isDragState()Z
    .locals 2

    .line 692
    iget v0, p0, Lcom/nkvt/SwitchStyle;->animateState:I

    const/4 v1, 0x2

    if-ne v0, v1, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method private isInAnimating()Z
    .locals 1

    .line 675
    iget v0, p0, Lcom/nkvt/SwitchStyle;->animateState:I

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method private isPendingDragState()Z
    .locals 3

    .line 683
    iget v0, p0, Lcom/nkvt/SwitchStyle;->animateState:I

    const/4 v1, 0x1

    if-eq v0, v1, :cond_1

    const/4 v2, 0x3

    if-ne v0, v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    :cond_1
    :goto_0
    return v1
.end method

.method private static optBoolean(Landroid/content/res/TypedArray;IZ)Z
    .locals 1
    .param p0, "typedArray"    # Landroid/content/res/TypedArray;
    .param p1, "index"    # I
    .param p2, "def"    # Z

    .line 808
    if-nez p0, :cond_0

    return p2

    .line 809
    :cond_0
    invoke-virtual {p0, p1, p2}, Landroid/content/res/TypedArray;->getBoolean(IZ)Z

    move-result v0

    return v0
.end method

.method private static optColor(Landroid/content/res/TypedArray;II)I
    .locals 1
    .param p0, "typedArray"    # Landroid/content/res/TypedArray;
    .param p1, "index"    # I
    .param p2, "def"    # I

    .line 801
    if-nez p0, :cond_0

    return p2

    .line 802
    :cond_0
    invoke-virtual {p0, p1, p2}, Landroid/content/res/TypedArray;->getColor(II)I

    move-result v0

    return v0
.end method

.method private static optInt(Landroid/content/res/TypedArray;II)I
    .locals 1
    .param p0, "typedArray"    # Landroid/content/res/TypedArray;
    .param p1, "index"    # I
    .param p2, "def"    # I

    .line 779
    if-nez p0, :cond_0

    return p2

    .line 780
    :cond_0
    invoke-virtual {p0, p1, p2}, Landroid/content/res/TypedArray;->getInt(II)I

    move-result v0

    return v0
.end method

.method private static optPixelSize(Landroid/content/res/TypedArray;IF)F
    .locals 1
    .param p0, "typedArray"    # Landroid/content/res/TypedArray;
    .param p1, "index"    # I
    .param p2, "def"    # F

    .line 787
    if-nez p0, :cond_0

    return p2

    .line 788
    :cond_0
    invoke-virtual {p0, p1, p2}, Landroid/content/res/TypedArray;->getDimension(IF)F

    move-result v0

    return v0
.end method

.method private static optPixelSize(Landroid/content/res/TypedArray;II)I
    .locals 1
    .param p0, "typedArray"    # Landroid/content/res/TypedArray;
    .param p1, "index"    # I
    .param p2, "def"    # I

    .line 794
    if-nez p0, :cond_0

    return p2

    .line 795
    :cond_0
    invoke-virtual {p0, p1, p2}, Landroid/content/res/TypedArray;->getDimensionPixelOffset(II)I

    move-result v0

    return v0
.end method

.method private pendingCancelDragState()V
    .locals 2

    .line 729
    invoke-direct {p0}, Lcom/nkvt/SwitchStyle;->isDragState()Z

    move-result v0

    if-nez v0, :cond_0

    invoke-direct {p0}, Lcom/nkvt/SwitchStyle;->isPendingDragState()Z

    move-result v0

    if-eqz v0, :cond_3

    .line 730
    :cond_0
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->valueAnimator:Landroid/animation/ValueAnimator;

    invoke-virtual {v0}, Landroid/animation/ValueAnimator;->isRunning()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 731
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->valueAnimator:Landroid/animation/ValueAnimator;

    invoke-virtual {v0}, Landroid/animation/ValueAnimator;->cancel()V

    .line 734
    :cond_1
    const/4 v0, 0x3

    iput v0, p0, Lcom/nkvt/SwitchStyle;->animateState:I

    .line 735
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->beforeState:Lcom/nkvt/SwitchStyle$ViewState;

    iget-object v1, p0, Lcom/nkvt/SwitchStyle;->viewState:Lcom/nkvt/SwitchStyle$ViewState;

    invoke-static {v0, v1}, Lcom/nkvt/SwitchStyle$ViewState;->access$000(Lcom/nkvt/SwitchStyle$ViewState;Lcom/nkvt/SwitchStyle$ViewState;)V

    .line 737
    invoke-virtual {p0}, Lcom/nkvt/SwitchStyle;->isChecked()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 738
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->afterState:Lcom/nkvt/SwitchStyle$ViewState;

    invoke-direct {p0, v0}, Lcom/nkvt/SwitchStyle;->setCheckedViewState(Lcom/nkvt/SwitchStyle$ViewState;)V

    goto :goto_0

    .line 740
    :cond_2
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->afterState:Lcom/nkvt/SwitchStyle$ViewState;

    invoke-direct {p0, v0}, Lcom/nkvt/SwitchStyle;->setUncheckViewState(Lcom/nkvt/SwitchStyle$ViewState;)V

    .line 742
    :goto_0
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->valueAnimator:Landroid/animation/ValueAnimator;

    invoke-virtual {v0}, Landroid/animation/ValueAnimator;->start()V

    .line 744
    :cond_3
    return-void
.end method

.method private pendingDragState()V
    .locals 2

    .line 699
    invoke-direct {p0}, Lcom/nkvt/SwitchStyle;->isInAnimating()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    .line 700
    :cond_0
    iget-boolean v0, p0, Lcom/nkvt/SwitchStyle;->isTouchingDown:Z

    if-nez v0, :cond_1

    return-void

    .line 702
    :cond_1
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->valueAnimator:Landroid/animation/ValueAnimator;

    invoke-virtual {v0}, Landroid/animation/ValueAnimator;->isRunning()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 703
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->valueAnimator:Landroid/animation/ValueAnimator;

    invoke-virtual {v0}, Landroid/animation/ValueAnimator;->cancel()V

    .line 706
    :cond_2
    const/4 v0, 0x1

    iput v0, p0, Lcom/nkvt/SwitchStyle;->animateState:I

    .line 708
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->beforeState:Lcom/nkvt/SwitchStyle$ViewState;

    iget-object v1, p0, Lcom/nkvt/SwitchStyle;->viewState:Lcom/nkvt/SwitchStyle$ViewState;

    invoke-static {v0, v1}, Lcom/nkvt/SwitchStyle$ViewState;->access$000(Lcom/nkvt/SwitchStyle$ViewState;Lcom/nkvt/SwitchStyle$ViewState;)V

    .line 709
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->afterState:Lcom/nkvt/SwitchStyle$ViewState;

    iget-object v1, p0, Lcom/nkvt/SwitchStyle;->viewState:Lcom/nkvt/SwitchStyle$ViewState;

    invoke-static {v0, v1}, Lcom/nkvt/SwitchStyle$ViewState;->access$000(Lcom/nkvt/SwitchStyle$ViewState;Lcom/nkvt/SwitchStyle$ViewState;)V

    .line 711
    invoke-virtual {p0}, Lcom/nkvt/SwitchStyle;->isChecked()Z

    move-result v0

    if-eqz v0, :cond_3

    .line 712
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->afterState:Lcom/nkvt/SwitchStyle$ViewState;

    iget v1, p0, Lcom/nkvt/SwitchStyle;->checkedColor:I

    iput v1, v0, Lcom/nkvt/SwitchStyle$ViewState;->checkStateColor:I

    .line 713
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->afterState:Lcom/nkvt/SwitchStyle$ViewState;

    iget v1, p0, Lcom/nkvt/SwitchStyle;->buttonMaxX:F

    iput v1, v0, Lcom/nkvt/SwitchStyle$ViewState;->buttonX:F

    .line 714
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->afterState:Lcom/nkvt/SwitchStyle$ViewState;

    iget v1, p0, Lcom/nkvt/SwitchStyle;->checkedColor:I

    iput v1, v0, Lcom/nkvt/SwitchStyle$ViewState;->checkedLineColor:I

    goto :goto_0

    .line 716
    :cond_3
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->afterState:Lcom/nkvt/SwitchStyle$ViewState;

    iget v1, p0, Lcom/nkvt/SwitchStyle;->uncheckColor:I

    iput v1, v0, Lcom/nkvt/SwitchStyle$ViewState;->checkStateColor:I

    .line 717
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->afterState:Lcom/nkvt/SwitchStyle$ViewState;

    iget v1, p0, Lcom/nkvt/SwitchStyle;->buttonMinX:F

    iput v1, v0, Lcom/nkvt/SwitchStyle$ViewState;->buttonX:F

    .line 718
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->afterState:Lcom/nkvt/SwitchStyle$ViewState;

    iget v1, p0, Lcom/nkvt/SwitchStyle;->viewRadius:F

    iput v1, v0, Lcom/nkvt/SwitchStyle$ViewState;->radius:F

    .line 721
    :goto_0
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->valueAnimator:Landroid/animation/ValueAnimator;

    invoke-virtual {v0}, Landroid/animation/ValueAnimator;->start()V

    .line 722
    return-void
.end method

.method private pendingSettleState()V
    .locals 2

    .line 751
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->valueAnimator:Landroid/animation/ValueAnimator;

    invoke-virtual {v0}, Landroid/animation/ValueAnimator;->isRunning()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 752
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->valueAnimator:Landroid/animation/ValueAnimator;

    invoke-virtual {v0}, Landroid/animation/ValueAnimator;->cancel()V

    .line 755
    :cond_0
    const/4 v0, 0x4

    iput v0, p0, Lcom/nkvt/SwitchStyle;->animateState:I

    .line 756
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->beforeState:Lcom/nkvt/SwitchStyle$ViewState;

    iget-object v1, p0, Lcom/nkvt/SwitchStyle;->viewState:Lcom/nkvt/SwitchStyle$ViewState;

    invoke-static {v0, v1}, Lcom/nkvt/SwitchStyle$ViewState;->access$000(Lcom/nkvt/SwitchStyle$ViewState;Lcom/nkvt/SwitchStyle$ViewState;)V

    .line 758
    invoke-virtual {p0}, Lcom/nkvt/SwitchStyle;->isChecked()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 759
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->afterState:Lcom/nkvt/SwitchStyle$ViewState;

    invoke-direct {p0, v0}, Lcom/nkvt/SwitchStyle;->setCheckedViewState(Lcom/nkvt/SwitchStyle$ViewState;)V

    goto :goto_0

    .line 761
    :cond_1
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->afterState:Lcom/nkvt/SwitchStyle$ViewState;

    invoke-direct {p0, v0}, Lcom/nkvt/SwitchStyle;->setUncheckViewState(Lcom/nkvt/SwitchStyle$ViewState;)V

    .line 763
    :goto_0
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->valueAnimator:Landroid/animation/ValueAnimator;

    invoke-virtual {v0}, Landroid/animation/ValueAnimator;->start()V

    .line 764
    return-void
.end method

.method private setCheckedViewState(Lcom/nkvt/SwitchStyle$ViewState;)V
    .locals 2
    .param p1, "viewState"    # Lcom/nkvt/SwitchStyle$ViewState;

    .line 326
    iget v0, p0, Lcom/nkvt/SwitchStyle;->viewRadius:F

    iput v0, p1, Lcom/nkvt/SwitchStyle$ViewState;->radius:F

    .line 327
    iget v0, p0, Lcom/nkvt/SwitchStyle;->checkedColor:I

    iput v0, p1, Lcom/nkvt/SwitchStyle$ViewState;->checkStateColor:I

    .line 328
    iget v0, p0, Lcom/nkvt/SwitchStyle;->checkLineColor:I

    iput v0, p1, Lcom/nkvt/SwitchStyle$ViewState;->checkedLineColor:I

    .line 329
    iget v0, p0, Lcom/nkvt/SwitchStyle;->buttonMaxX:F

    iput v0, p1, Lcom/nkvt/SwitchStyle$ViewState;->buttonX:F

    .line 330
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->buttonPaint:Landroid/graphics/Paint;

    iget v1, p0, Lcom/nkvt/SwitchStyle;->checkedButtonColor:I

    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setColor(I)V

    .line 331
    return-void
.end method

.method private setUncheckViewState(Lcom/nkvt/SwitchStyle$ViewState;)V
    .locals 2
    .param p1, "viewState"    # Lcom/nkvt/SwitchStyle$ViewState;

    .line 317
    const/4 v0, 0x0

    iput v0, p1, Lcom/nkvt/SwitchStyle$ViewState;->radius:F

    .line 318
    iget v0, p0, Lcom/nkvt/SwitchStyle;->uncheckColor:I

    iput v0, p1, Lcom/nkvt/SwitchStyle$ViewState;->checkStateColor:I

    .line 319
    const/4 v0, 0x0

    iput v0, p1, Lcom/nkvt/SwitchStyle$ViewState;->checkedLineColor:I

    .line 320
    iget v0, p0, Lcom/nkvt/SwitchStyle;->buttonMinX:F

    iput v0, p1, Lcom/nkvt/SwitchStyle$ViewState;->buttonX:F

    .line 321
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->buttonPaint:Landroid/graphics/Paint;

    iget v1, p0, Lcom/nkvt/SwitchStyle;->uncheckButtonColor:I

    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setColor(I)V

    .line 322
    return-void
.end method

.method private toggle(ZZ)V
    .locals 2
    .param p1, "animate"    # Z
    .param p2, "broadcast"    # Z

    .line 527
    invoke-virtual {p0}, Lcom/nkvt/SwitchStyle;->isEnabled()Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    .line 529
    :cond_0
    iget-boolean v0, p0, Lcom/nkvt/SwitchStyle;->isEventBroadcast:Z

    if-nez v0, :cond_9

    .line 532
    iget-boolean v0, p0, Lcom/nkvt/SwitchStyle;->isUiInited:Z

    if-nez v0, :cond_2

    .line 533
    iget-boolean v0, p0, Lcom/nkvt/SwitchStyle;->isChecked:Z

    xor-int/lit8 v0, v0, 0x1

    iput-boolean v0, p0, Lcom/nkvt/SwitchStyle;->isChecked:Z

    .line 534
    if-eqz p2, :cond_1

    .line 535
    invoke-direct {p0}, Lcom/nkvt/SwitchStyle;->broadcastEvent()V

    .line 537
    :cond_1
    return-void

    .line 540
    :cond_2
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->valueAnimator:Landroid/animation/ValueAnimator;

    invoke-virtual {v0}, Landroid/animation/ValueAnimator;->isRunning()Z

    move-result v0

    if-eqz v0, :cond_3

    .line 541
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->valueAnimator:Landroid/animation/ValueAnimator;

    invoke-virtual {v0}, Landroid/animation/ValueAnimator;->cancel()V

    .line 544
    :cond_3
    iget-boolean v0, p0, Lcom/nkvt/SwitchStyle;->enableEffect:Z

    if-eqz v0, :cond_6

    if-nez p1, :cond_4

    goto :goto_1

    .line 558
    :cond_4
    const/4 v0, 0x5

    iput v0, p0, Lcom/nkvt/SwitchStyle;->animateState:I

    .line 559
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->beforeState:Lcom/nkvt/SwitchStyle$ViewState;

    iget-object v1, p0, Lcom/nkvt/SwitchStyle;->viewState:Lcom/nkvt/SwitchStyle$ViewState;

    invoke-static {v0, v1}, Lcom/nkvt/SwitchStyle$ViewState;->access$000(Lcom/nkvt/SwitchStyle$ViewState;Lcom/nkvt/SwitchStyle$ViewState;)V

    .line 561
    invoke-virtual {p0}, Lcom/nkvt/SwitchStyle;->isChecked()Z

    move-result v0

    if-eqz v0, :cond_5

    .line 563
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->afterState:Lcom/nkvt/SwitchStyle$ViewState;

    invoke-direct {p0, v0}, Lcom/nkvt/SwitchStyle;->setUncheckViewState(Lcom/nkvt/SwitchStyle$ViewState;)V

    goto :goto_0

    .line 565
    :cond_5
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->afterState:Lcom/nkvt/SwitchStyle$ViewState;

    invoke-direct {p0, v0}, Lcom/nkvt/SwitchStyle;->setCheckedViewState(Lcom/nkvt/SwitchStyle$ViewState;)V

    .line 567
    :goto_0
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->valueAnimator:Landroid/animation/ValueAnimator;

    invoke-virtual {v0}, Landroid/animation/ValueAnimator;->start()V

    .line 568
    return-void

    .line 545
    :cond_6
    :goto_1
    iget-boolean v0, p0, Lcom/nkvt/SwitchStyle;->isChecked:Z

    xor-int/lit8 v0, v0, 0x1

    iput-boolean v0, p0, Lcom/nkvt/SwitchStyle;->isChecked:Z

    .line 546
    invoke-virtual {p0}, Lcom/nkvt/SwitchStyle;->isChecked()Z

    move-result v0

    if-eqz v0, :cond_7

    .line 547
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->viewState:Lcom/nkvt/SwitchStyle$ViewState;

    invoke-direct {p0, v0}, Lcom/nkvt/SwitchStyle;->setCheckedViewState(Lcom/nkvt/SwitchStyle$ViewState;)V

    goto :goto_2

    .line 549
    :cond_7
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->viewState:Lcom/nkvt/SwitchStyle$ViewState;

    invoke-direct {p0, v0}, Lcom/nkvt/SwitchStyle;->setUncheckViewState(Lcom/nkvt/SwitchStyle$ViewState;)V

    .line 551
    :goto_2
    invoke-virtual {p0}, Lcom/nkvt/SwitchStyle;->postInvalidate()V

    .line 552
    if-eqz p2, :cond_8

    .line 553
    invoke-direct {p0}, Lcom/nkvt/SwitchStyle;->broadcastEvent()V

    .line 555
    :cond_8
    return-void

    .line 530
    :cond_9
    new-instance v0, Ljava/lang/RuntimeException;

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->IJsbWˆˑיﹶᐧˆᵔᐧˆיﹳⁱˉˊrirRM()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    throw v0
.end method


# virtual methods
.method protected drawCheckedIndicator(Landroid/graphics/Canvas;)V
    .locals 10
    .param p1, "canvas"    # Landroid/graphics/Canvas;

    .line 391
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->viewState:Lcom/nkvt/SwitchStyle$ViewState;

    iget v3, v0, Lcom/nkvt/SwitchStyle$ViewState;->checkedLineColor:I

    iget v0, p0, Lcom/nkvt/SwitchStyle;->checkLineWidth:I

    int-to-float v4, v0

    iget v0, p0, Lcom/nkvt/SwitchStyle;->left:F

    iget v1, p0, Lcom/nkvt/SwitchStyle;->viewRadius:F

    add-float v2, v0, v1

    iget v5, p0, Lcom/nkvt/SwitchStyle;->checkedLineOffsetX:F

    sub-float v5, v2, v5

    iget v2, p0, Lcom/nkvt/SwitchStyle;->centerY:F

    iget v6, p0, Lcom/nkvt/SwitchStyle;->checkLineLength:F

    sub-float v7, v2, v6

    add-float/2addr v0, v1

    iget v1, p0, Lcom/nkvt/SwitchStyle;->checkedLineOffsetY:F

    sub-float/2addr v0, v1

    add-float v8, v2, v6

    iget-object v9, p0, Lcom/nkvt/SwitchStyle;->paint:Landroid/graphics/Paint;

    move-object v1, p0

    move-object v2, p1

    move v6, v7

    move v7, v0

    invoke-virtual/range {v1 .. v9}, Lcom/nkvt/SwitchStyle;->drawCheckedIndicator(Landroid/graphics/Canvas;IFFFFFLandroid/graphics/Paint;)V

    .line 397
    return-void
.end method

.method protected drawCheckedIndicator(Landroid/graphics/Canvas;IFFFFFLandroid/graphics/Paint;)V
    .locals 6
    .param p1, "canvas"    # Landroid/graphics/Canvas;
    .param p2, "color"    # I
    .param p3, "lineWidth"    # F
    .param p4, "sx"    # F
    .param p5, "sy"    # F
    .param p6, "ex"    # F
    .param p7, "ey"    # F
    .param p8, "paint"    # Landroid/graphics/Paint;

    .line 416
    sget-object v0, Landroid/graphics/Paint$Style;->STROKE:Landroid/graphics/Paint$Style;

    invoke-virtual {p8, v0}, Landroid/graphics/Paint;->setStyle(Landroid/graphics/Paint$Style;)V

    .line 417
    invoke-virtual {p8, p2}, Landroid/graphics/Paint;->setColor(I)V

    .line 418
    invoke-virtual {p8, p3}, Landroid/graphics/Paint;->setStrokeWidth(F)V

    .line 419
    move-object v0, p1

    move v1, p4

    move v2, p5

    move v3, p6

    move v4, p7

    move-object v5, p8

    invoke-virtual/range {v0 .. v5}, Landroid/graphics/Canvas;->drawLine(FFFFLandroid/graphics/Paint;)V

    .line 422
    return-void
.end method

.method protected drawUncheckIndicator(Landroid/graphics/Canvas;IFFFFLandroid/graphics/Paint;)V
    .locals 1
    .param p1, "canvas"    # Landroid/graphics/Canvas;
    .param p2, "color"    # I
    .param p3, "lineWidth"    # F
    .param p4, "centerX"    # F
    .param p5, "centerY"    # F
    .param p6, "radius"    # F
    .param p7, "paint"    # Landroid/graphics/Paint;

    .line 454
    sget-object v0, Landroid/graphics/Paint$Style;->STROKE:Landroid/graphics/Paint$Style;

    invoke-virtual {p7, v0}, Landroid/graphics/Paint;->setStyle(Landroid/graphics/Paint$Style;)V

    .line 455
    invoke-virtual {p7, p2}, Landroid/graphics/Paint;->setColor(I)V

    .line 456
    const/4 v0, 0x0

    invoke-virtual {p7, v0}, Landroid/graphics/Paint;->setStrokeWidth(F)V

    .line 457
    invoke-virtual {p1, p4, p5, p6, p7}, Landroid/graphics/Canvas;->drawCircle(FFFLandroid/graphics/Paint;)V

    .line 458
    return-void
.end method

.method public isChecked()Z
    .locals 1

    .line 91
    iget-boolean v0, p0, Lcom/nkvt/SwitchStyle;->isChecked:Z

    return v0
.end method

.method protected onDraw(Landroid/graphics/Canvas;)V
    .locals 13
    .param p1, "canvas"    # Landroid/graphics/Canvas;

    .line 335
    invoke-super {p0, p1}, Landroid/view/View;->onDraw(Landroid/graphics/Canvas;)V

    .line 337
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->paint:Landroid/graphics/Paint;

    sget-object v1, Landroid/graphics/Paint$Style;->FILL:Landroid/graphics/Paint$Style;

    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setStyle(Landroid/graphics/Paint$Style;)V

    .line 339
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->paint:Landroid/graphics/Paint;

    const/4 v1, -0x1

    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setColor(I)V

    .line 340
    iget v4, p0, Lcom/nkvt/SwitchStyle;->left:F

    iget v5, p0, Lcom/nkvt/SwitchStyle;->top:F

    iget v6, p0, Lcom/nkvt/SwitchStyle;->right:F

    iget v7, p0, Lcom/nkvt/SwitchStyle;->bottom:F

    iget v8, p0, Lcom/nkvt/SwitchStyle;->viewRadius:F

    iget-object v9, p0, Lcom/nkvt/SwitchStyle;->paint:Landroid/graphics/Paint;

    move-object v2, p0

    move-object v3, p1

    invoke-direct/range {v2 .. v9}, Lcom/nkvt/SwitchStyle;->drawRoundRect(Landroid/graphics/Canvas;FFFFFLandroid/graphics/Paint;)V

    .line 344
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->paint:Landroid/graphics/Paint;

    sget-object v1, Landroid/graphics/Paint$Style;->STROKE:Landroid/graphics/Paint$Style;

    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setStyle(Landroid/graphics/Paint$Style;)V

    .line 345
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->paint:Landroid/graphics/Paint;

    iget v1, p0, Lcom/nkvt/SwitchStyle;->uncheckColor:I

    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setColor(I)V

    .line 346
    iget v4, p0, Lcom/nkvt/SwitchStyle;->left:F

    iget v5, p0, Lcom/nkvt/SwitchStyle;->top:F

    iget v6, p0, Lcom/nkvt/SwitchStyle;->right:F

    iget v7, p0, Lcom/nkvt/SwitchStyle;->bottom:F

    iget v8, p0, Lcom/nkvt/SwitchStyle;->viewRadius:F

    iget-object v9, p0, Lcom/nkvt/SwitchStyle;->paint:Landroid/graphics/Paint;

    invoke-direct/range {v2 .. v9}, Lcom/nkvt/SwitchStyle;->drawRoundRect(Landroid/graphics/Canvas;FFFFFLandroid/graphics/Paint;)V

    .line 351
    iget-boolean v0, p0, Lcom/nkvt/SwitchStyle;->showIndicator:Z

    if-eqz v0, :cond_0

    .line 352
    invoke-direct {p0, p1}, Lcom/nkvt/SwitchStyle;->drawUncheckIndicator(Landroid/graphics/Canvas;)V

    .line 356
    :cond_0
    iget-object v0, p0, Lcom/nkvt/SwitchStyle;->viewState:Lcom/nkvt/SwitchStyle$ViewState;

    iget v0, v0, Lcom/nkvt/SwitchStyle$ViewState;->radius:F

    const/high16 v1, 0x3f000000    # 0.5f

    mul-float v0, v0, v1

    .line 357
    .local v0, "des":F
    iget-object v1, p0, Lcom/nkvt/SwitchStyle;->paint:Landroid/graphics/Paint;

    sget-object v2, Landroid/graphics/Paint$Style;->STROKE:Landroid/graphics/Paint$Style;

    invoke-virtual {v1, v2}, Landroid/graphics/Paint;->setStyle(Landroid/graphics/Paint$Style;)V

    .line 358
    iget-object v1, p0, Lcom/nkvt/SwitchStyle;->paint:Landroid/graphics/Paint;

    iget-object v2, p0, Lcom/nkvt/SwitchStyle;->viewState:Lcom/nkvt/SwitchStyle$ViewState;

    iget v2, v2, Lcom/nkvt/SwitchStyle$ViewState;->checkStateColor:I

    invoke-virtual {v1, v2}, Landroid/graphics/Paint;->setColor(I)V

    .line 359
    iget-object v1, p0, Lcom/nkvt/SwitchStyle;->paint:Landroid/graphics/Paint;

    iget v2, p0, Lcom/nkvt/SwitchStyle;->borderWidth:I

    int-to-float v2, v2

    const/high16 v3, 0x40000000    # 2.0f

    mul-float v4, v0, v3

    add-float/2addr v2, v4

    invoke-virtual {v1, v2}, Landroid/graphics/Paint;->setStrokeWidth(F)V

    .line 360
    iget v1, p0, Lcom/nkvt/SwitchStyle;->left:F

    add-float v6, v1, v0

    iget v1, p0, Lcom/nkvt/SwitchStyle;->top:F

    add-float v7, v1, v0

    iget v1, p0, Lcom/nkvt/SwitchStyle;->right:F

    sub-float v8, v1, v0

    iget v1, p0, Lcom/nkvt/SwitchStyle;->bottom:F

    sub-float v9, v1, v0

    iget v10, p0, Lcom/nkvt/SwitchStyle;->viewRadius:F

    iget-object v11, p0, Lcom/nkvt/SwitchStyle;->paint:Landroid/graphics/Paint;

    move-object v4, p0

    move-object v5, p1

    invoke-direct/range {v4 .. v11}, Lcom/nkvt/SwitchStyle;->drawRoundRect(Landroid/graphics/Canvas;FFFFFLandroid/graphics/Paint;)V

    .line 365
    iget-object v1, p0, Lcom/nkvt/SwitchStyle;->paint:Landroid/graphics/Paint;

    sget-object v2, Landroid/graphics/Paint$Style;->FILL:Landroid/graphics/Paint$Style;

    invoke-virtual {v1, v2}, Landroid/graphics/Paint;->setStyle(Landroid/graphics/Paint$Style;)V

    .line 366
    iget-object v1, p0, Lcom/nkvt/SwitchStyle;->paint:Landroid/graphics/Paint;

    const/4 v2, 0x0

    invoke-virtual {v1, v2}, Landroid/graphics/Paint;->setStrokeWidth(F)V

    .line 367
    iget v6, p0, Lcom/nkvt/SwitchStyle;->left:F

    iget v7, p0, Lcom/nkvt/SwitchStyle;->top:F

    iget v1, p0, Lcom/nkvt/SwitchStyle;->viewRadius:F

    mul-float v2, v1, v3

    add-float v8, v6, v2

    mul-float v1, v1, v3

    add-float v9, v7, v1

    const/high16 v10, 0x42b40000    # 90.0f

    const/high16 v11, 0x43340000    # 180.0f

    iget-object v12, p0, Lcom/nkvt/SwitchStyle;->paint:Landroid/graphics/Paint;

    invoke-direct/range {v4 .. v12}, Lcom/nkvt/SwitchStyle;->drawArc(Landroid/graphics/Canvas;FFFFFFLandroid/graphics/Paint;)V

    .line 371
    iget v1, p0, Lcom/nkvt/SwitchStyle;->left:F

    iget v2, p0, Lcom/nkvt/SwitchStyle;->viewRadius:F

    add-float v5, v1, v2

    iget v6, p0, Lcom/nkvt/SwitchStyle;->top:F

    iget-object v1, p0, Lcom/nkvt/SwitchStyle;->viewState:Lcom/nkvt/SwitchStyle$ViewState;

    iget v7, v1, Lcom/nkvt/SwitchStyle$ViewState;->buttonX:F

    iget v1, p0, Lcom/nkvt/SwitchStyle;->top:F

    iget v2, p0, Lcom/nkvt/SwitchStyle;->viewRadius:F

    mul-float v2, v2, v3

    add-float v8, v1, v2

    iget-object v9, p0, Lcom/nkvt/SwitchStyle;->paint:Landroid/graphics/Paint;

    move-object v4, p1

    invoke-virtual/range {v4 .. v9}, Landroid/graphics/Canvas;->drawRect(FFFFLandroid/graphics/Paint;)V

    .line 377
    iget-boolean v1, p0, Lcom/nkvt/SwitchStyle;->showIndicator:Z

    if-eqz v1, :cond_1

    .line 378
    invoke-virtual {p0, p1}, Lcom/nkvt/SwitchStyle;->drawCheckedIndicator(Landroid/graphics/Canvas;)V

    .line 382
    :cond_1
    iget-object v1, p0, Lcom/nkvt/SwitchStyle;->viewState:Lcom/nkvt/SwitchStyle$ViewState;

    iget v1, v1, Lcom/nkvt/SwitchStyle$ViewState;->buttonX:F

    iget v2, p0, Lcom/nkvt/SwitchStyle;->centerY:F

    invoke-direct {p0, p1, v1, v2}, Lcom/nkvt/SwitchStyle;->drawButton(Landroid/graphics/Canvas;FF)V

    .line 383
    return-void
.end method

.method protected onMeasure(II)V
    .locals 5
    .param p1, "widthMeasureSpec"    # I
    .param p2, "heightMeasureSpec"    # I

    .line 265
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getMode(I)I

    move-result v0

    .line 266
    .local v0, "widthMode":I
    invoke-static {p2}, Landroid/view/View$MeasureSpec;->getMode(I)I

    move-result v1

    .line 268
    .local v1, "heightMode":I
    const/high16 v2, -0x80000000

    const/high16 v3, 0x40000000    # 2.0f

    if-eqz v0, :cond_0

    if-ne v0, v2, :cond_1

    .line 270
    :cond_0
    sget v4, Lcom/nkvt/SwitchStyle;->DEFAULT_WIDTH:I

    invoke-static {v4, v3}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    move-result p1

    .line 272
    :cond_1
    if-eqz v1, :cond_2

    if-ne v1, v2, :cond_3

    .line 274
    :cond_2
    sget v2, Lcom/nkvt/SwitchStyle;->DEFAULT_HEIGHT:I

    invoke-static {v2, v3}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    move-result p2

    .line 276
    :cond_3
    invoke-super {p0, p1, p2}, Landroid/view/View;->onMeasure(II)V

    .line 277
    return-void
.end method

.method protected onSizeChanged(IIII)V
    .locals 6
    .param p1, "w"    # I
    .param p2, "h"    # I
    .param p3, "oldw"    # I
    .param p4, "oldh"    # I

    .line 281
    invoke-super {p0, p1, p2, p3, p4}, Landroid/view/View;->onSizeChanged(IIII)V

    .line 284
    iget v0, p0, Lcom/nkvt/SwitchStyle;->shadowRadius:I

    iget v1, p0, Lcom/nkvt/SwitchStyle;->shadowOffset:I

    add-int/2addr v0, v1

    iget v1, p0, Lcom/nkvt/SwitchStyle;->borderWidth:I

    invoke-static {v0, v1}, Ljava/lang/Math;->max(II)I

    move-result v0

    int-to-float v0, v0

    .line 286
    .local v0, "viewPadding":F
    int-to-float v1, p2

    sub-float/2addr v1, v0

    sub-float/2addr v1, v0

    iput v1, p0, Lcom/nkvt/SwitchStyle;->height:F

    .line 287
    int-to-float v2, p1

    sub-float/2addr v2, v0

    sub-float/2addr v2, v0

    iput v2, p0, Lcom/nkvt/SwitchStyle;->width:F

    .line 289
    const/high16 v2, 0x3f000000    # 0.5f

    mul-float v1, v1, v2

    iput v1, p0, Lcom/nkvt/SwitchStyle;->viewRadius:F

    .line 290
    iget v3, p0, Lcom/nkvt/SwitchStyle;->borderWidth:I

    int-to-float v3, v3

    sub-float v3, v1, v3

    iput v3, p0, Lcom/nkvt/SwitchStyle;->buttonRadius:F

    .line 292
    iput v0, p0, Lcom/nkvt/SwitchStyle;->left:F

    .line 293
    iput v0, p0, Lcom/nkvt/SwitchStyle;->top:F

    .line 294
    int-to-float v3, p1

    sub-float/2addr v3, v0

    iput v3, p0, Lcom/nkvt/SwitchStyle;->right:F

    .line 295
    int-to-float v4, p2

    sub-float/2addr v4, v0

    iput v4, p0, Lcom/nkvt/SwitchStyle;->bottom:F

    .line 297
    add-float v5, v0, v3

    mul-float v5, v5, v2

    iput v5, p0, Lcom/nkvt/SwitchStyle;->centerX:F

    .line 298
    add-float/2addr v4, v0

    mul-float v4, v4, v2

    iput v4, p0, Lcom/nkvt/SwitchStyle;->centerY:F

    .line 300
    add-float v2, v0, v1

    iput v2, p0, Lcom/nkvt/SwitchStyle;->buttonMinX:F

    .line 301
    sub-float/2addr v3, v1

    iput v3, p0, Lcom/nkvt/SwitchStyle;->buttonMaxX:F

    .line 303
    invoke-virtual {p0}, Lcom/nkvt/SwitchStyle;->isChecked()Z

    move-result v1

    if-eqz v1, :cond_0

    .line 304
    iget-object v1, p0, Lcom/nkvt/SwitchStyle;->viewState:Lcom/nkvt/SwitchStyle$ViewState;

    invoke-direct {p0, v1}, Lcom/nkvt/SwitchStyle;->setCheckedViewState(Lcom/nkvt/SwitchStyle$ViewState;)V

    goto :goto_0

    .line 306
    :cond_0
    iget-object v1, p0, Lcom/nkvt/SwitchStyle;->viewState:Lcom/nkvt/SwitchStyle$ViewState;

    invoke-direct {p0, v1}, Lcom/nkvt/SwitchStyle;->setUncheckViewState(Lcom/nkvt/SwitchStyle$ViewState;)V

    .line 309
    :goto_0
    const/4 v1, 0x1

    iput-boolean v1, p0, Lcom/nkvt/SwitchStyle;->isUiInited:Z

    .line 311
    invoke-virtual {p0}, Lcom/nkvt/SwitchStyle;->postInvalidate()V

    .line 313
    return-void
.end method

.method public onTouchEvent(Landroid/view/MotionEvent;)Z
    .locals 10
    .param p1, "event"    # Landroid/view/MotionEvent;

    .line 584
    invoke-virtual {p0}, Lcom/nkvt/SwitchStyle;->isEnabled()Z

    move-result v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    .line 585
    :cond_0
    invoke-virtual {p1}, Landroid/view/MotionEvent;->getActionMasked()I

    move-result v0

    .line 587
    .local v0, "actionMasked":I
    const/4 v2, 0x1

    if-eqz v0, :cond_c

    const/high16 v3, 0x3f800000    # 1.0f

    const/4 v4, 0x0

    if-eq v0, v2, :cond_6

    const/4 v5, 0x2

    if-eq v0, v5, :cond_3

    const/4 v3, 0x3

    if-eq v0, v3, :cond_1

    goto/16 :goto_2

    .line 654
    :cond_1
    iput-boolean v1, p0, Lcom/nkvt/SwitchStyle;->isTouchingDown:Z

    .line 656
    iget-object v1, p0, Lcom/nkvt/SwitchStyle;->postPendingDrag:Ljava/lang/Runnable;

    invoke-virtual {p0, v1}, Lcom/nkvt/SwitchStyle;->removeCallbacks(Ljava/lang/Runnable;)Z

    .line 658
    invoke-direct {p0}, Lcom/nkvt/SwitchStyle;->isPendingDragState()Z

    move-result v1

    if-nez v1, :cond_2

    .line 659
    invoke-direct {p0}, Lcom/nkvt/SwitchStyle;->isDragState()Z

    move-result v1

    if-eqz v1, :cond_d

    .line 661
    :cond_2
    invoke-direct {p0}, Lcom/nkvt/SwitchStyle;->pendingCancelDragState()V

    goto/16 :goto_2

    .line 598
    :cond_3
    invoke-virtual {p1}, Landroid/view/MotionEvent;->getX()F

    move-result v1

    .line 599
    .local v1, "eventX":F
    invoke-direct {p0}, Lcom/nkvt/SwitchStyle;->isPendingDragState()Z

    move-result v5

    if-eqz v5, :cond_4

    .line 601
    invoke-virtual {p0}, Lcom/nkvt/SwitchStyle;->getWidth()I

    move-result v5

    int-to-float v5, v5

    div-float v5, v1, v5

    .line 602
    .local v5, "fraction":F
    invoke-static {v3, v5}, Ljava/lang/Math;->min(FF)F

    move-result v3

    invoke-static {v4, v3}, Ljava/lang/Math;->max(FF)F

    move-result v3

    .line 604
    .end local v5    # "fraction":F
    .local v3, "fraction":F
    iget-object v4, p0, Lcom/nkvt/SwitchStyle;->viewState:Lcom/nkvt/SwitchStyle$ViewState;

    iget v5, p0, Lcom/nkvt/SwitchStyle;->buttonMinX:F

    iget v6, p0, Lcom/nkvt/SwitchStyle;->buttonMaxX:F

    sub-float/2addr v6, v5

    mul-float v6, v6, v3

    add-float/2addr v5, v6

    iput v5, v4, Lcom/nkvt/SwitchStyle$ViewState;->buttonX:F

    .end local v3    # "fraction":F
    goto :goto_0

    .line 608
    :cond_4
    invoke-direct {p0}, Lcom/nkvt/SwitchStyle;->isDragState()Z

    move-result v5

    if-eqz v5, :cond_5

    .line 610
    invoke-virtual {p0}, Lcom/nkvt/SwitchStyle;->getWidth()I

    move-result v5

    int-to-float v5, v5

    div-float v5, v1, v5

    .line 611
    .restart local v5    # "fraction":F
    invoke-static {v3, v5}, Ljava/lang/Math;->min(FF)F

    move-result v3

    invoke-static {v4, v3}, Ljava/lang/Math;->max(FF)F

    move-result v3

    .line 613
    .end local v5    # "fraction":F
    .restart local v3    # "fraction":F
    iget-object v4, p0, Lcom/nkvt/SwitchStyle;->viewState:Lcom/nkvt/SwitchStyle$ViewState;

    iget v5, p0, Lcom/nkvt/SwitchStyle;->buttonMinX:F

    iget v6, p0, Lcom/nkvt/SwitchStyle;->buttonMaxX:F

    sub-float/2addr v6, v5

    mul-float v6, v6, v3

    add-float/2addr v5, v6

    iput v5, v4, Lcom/nkvt/SwitchStyle$ViewState;->buttonX:F

    .line 617
    iget-object v4, p0, Lcom/nkvt/SwitchStyle;->viewState:Lcom/nkvt/SwitchStyle$ViewState;

    iget-object v5, p0, Lcom/nkvt/SwitchStyle;->argbEvaluator:Landroid/animation/ArgbEvaluator;

    iget v6, p0, Lcom/nkvt/SwitchStyle;->uncheckColor:I

    .line 619
    invoke-static {v6}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v6

    iget v7, p0, Lcom/nkvt/SwitchStyle;->checkedColor:I

    .line 620
    invoke-static {v7}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v7

    .line 617
    invoke-virtual {v5, v3, v6, v7}, Landroid/animation/ArgbEvaluator;->evaluate(FLjava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Ljava/lang/Integer;

    invoke-virtual {v5}, Ljava/lang/Integer;->intValue()I

    move-result v5

    iput v5, v4, Lcom/nkvt/SwitchStyle$ViewState;->checkStateColor:I

    .line 622
    invoke-virtual {p0}, Lcom/nkvt/SwitchStyle;->postInvalidate()V

    .line 624
    .end local v3    # "fraction":F
    goto :goto_2

    .line 608
    :cond_5
    :goto_0
    goto :goto_2

    .line 628
    .end local v1    # "eventX":F
    :cond_6
    iput-boolean v1, p0, Lcom/nkvt/SwitchStyle;->isTouchingDown:Z

    .line 630
    iget-object v5, p0, Lcom/nkvt/SwitchStyle;->postPendingDrag:Ljava/lang/Runnable;

    invoke-virtual {p0, v5}, Lcom/nkvt/SwitchStyle;->removeCallbacks(Ljava/lang/Runnable;)Z

    .line 632
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v5

    iget-wide v7, p0, Lcom/nkvt/SwitchStyle;->touchDownTime:J

    sub-long/2addr v5, v7

    const-wide/16 v7, 0x12c

    cmp-long v9, v5, v7

    if-gtz v9, :cond_7

    .line 634
    invoke-virtual {p0}, Lcom/nkvt/SwitchStyle;->toggle()V

    goto :goto_2

    .line 635
    :cond_7
    invoke-direct {p0}, Lcom/nkvt/SwitchStyle;->isDragState()Z

    move-result v5

    if-eqz v5, :cond_a

    .line 637
    invoke-virtual {p1}, Landroid/view/MotionEvent;->getX()F

    move-result v5

    .line 638
    .local v5, "eventX":F
    invoke-virtual {p0}, Lcom/nkvt/SwitchStyle;->getWidth()I

    move-result v6

    int-to-float v6, v6

    div-float v6, v5, v6

    .line 639
    .local v6, "fraction":F
    invoke-static {v3, v6}, Ljava/lang/Math;->min(FF)F

    move-result v3

    invoke-static {v4, v3}, Ljava/lang/Math;->max(FF)F

    move-result v3

    .line 640
    .end local v6    # "fraction":F
    .restart local v3    # "fraction":F
    const/high16 v4, 0x3f000000    # 0.5f

    cmpl-float v4, v3, v4

    if-lez v4, :cond_8

    const/4 v1, 0x1

    .line 641
    .local v1, "newCheck":Z
    :cond_8
    invoke-virtual {p0}, Lcom/nkvt/SwitchStyle;->isChecked()Z

    move-result v4

    if-ne v1, v4, :cond_9

    .line 642
    invoke-direct {p0}, Lcom/nkvt/SwitchStyle;->pendingCancelDragState()V

    goto :goto_1

    .line 644
    :cond_9
    iput-boolean v1, p0, Lcom/nkvt/SwitchStyle;->isChecked:Z

    .line 645
    invoke-direct {p0}, Lcom/nkvt/SwitchStyle;->pendingSettleState()V

    goto :goto_1

    .line 647
    .end local v1    # "newCheck":Z
    .end local v3    # "fraction":F
    .end local v5    # "eventX":F
    :cond_a
    invoke-direct {p0}, Lcom/nkvt/SwitchStyle;->isPendingDragState()Z

    move-result v1

    if-eqz v1, :cond_b

    .line 649
    invoke-direct {p0}, Lcom/nkvt/SwitchStyle;->pendingCancelDragState()V

    goto :goto_2

    .line 647
    :cond_b
    :goto_1
    goto :goto_2

    .line 589
    :cond_c
    iput-boolean v2, p0, Lcom/nkvt/SwitchStyle;->isTouchingDown:Z

    .line 590
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v3

    iput-wide v3, p0, Lcom/nkvt/SwitchStyle;->touchDownTime:J

    .line 592
    iget-object v1, p0, Lcom/nkvt/SwitchStyle;->postPendingDrag:Ljava/lang/Runnable;

    invoke-virtual {p0, v1}, Lcom/nkvt/SwitchStyle;->removeCallbacks(Ljava/lang/Runnable;)Z

    .line 594
    iget-object v1, p0, Lcom/nkvt/SwitchStyle;->postPendingDrag:Ljava/lang/Runnable;

    const-wide/16 v3, 0x32

    invoke-virtual {p0, v1, v3, v4}, Lcom/nkvt/SwitchStyle;->postDelayed(Ljava/lang/Runnable;J)Z

    .line 595
    nop

    .line 666
    :cond_d
    :goto_2
    return v2
.end method

.method public setButtonEdgeFrame(Z)V
    .locals 0
    .param p1, "value"    # Z

    .line 108
    iput-boolean p1, p0, Lcom/nkvt/SwitchStyle;->buttonEdgeFrame:Z

    .line 109
    return-void
.end method

.method public setChecked(Z)V
    .locals 2
    .param p1, "checked"    # Z

    .line 82
    invoke-virtual {p0}, Lcom/nkvt/SwitchStyle;->isChecked()Z

    move-result v0

    if-ne p1, v0, :cond_0

    .line 83
    invoke-virtual {p0}, Lcom/nkvt/SwitchStyle;->postInvalidate()V

    .line 84
    return-void

    .line 86
    :cond_0
    iget-boolean v0, p0, Lcom/nkvt/SwitchStyle;->enableEffect:Z

    const/4 v1, 0x0

    invoke-direct {p0, v0, v1}, Lcom/nkvt/SwitchStyle;->toggle(ZZ)V

    .line 87
    return-void
.end method

.method public setCheckedButtonColor(I)V
    .locals 0
    .param p1, "checkedButtonColor"    # I

    .line 100
    iput p1, p0, Lcom/nkvt/SwitchStyle;->checkedButtonColor:I

    .line 101
    return-void
.end method

.method public setEnableEffect(Z)V
    .locals 0
    .param p1, "enable"    # Z

    .line 133
    iput-boolean p1, p0, Lcom/nkvt/SwitchStyle;->enableEffect:Z

    .line 134
    return-void
.end method

.method public setOnCheckedChangeListener(Lcom/nkvt/SwitchStyle$OnCheckedChangeListener;)V
    .locals 0
    .param p1, "l"    # Lcom/nkvt/SwitchStyle$OnCheckedChangeListener;

    .line 68
    iput-object p1, p0, Lcom/nkvt/SwitchStyle;->onCheckedChangeListener:Lcom/nkvt/SwitchStyle$OnCheckedChangeListener;

    .line 69
    return-void
.end method

.method public final setOnClickListener(Landroid/view/View$OnClickListener;)V
    .locals 0
    .param p1, "l"    # Landroid/view/View$OnClickListener;

    .line 62
    return-void
.end method

.method public final setOnLongClickListener(Landroid/view/View$OnLongClickListener;)V
    .locals 0
    .param p1, "l"    # Landroid/view/View$OnLongClickListener;

    .line 65
    return-void
.end method

.method public final setPadding(IIII)V
    .locals 1
    .param p1, "left"    # I
    .param p2, "top"    # I
    .param p3, "right"    # I
    .param p4, "bottom"    # I

    .line 77
    const/4 v0, 0x0

    invoke-super {p0, v0, v0, v0, v0}, Landroid/view/View;->setPadding(IIII)V

    .line 78
    return-void
.end method

.method public setShadowEffect(Z)V
    .locals 5
    .param p1, "shadowEffect"    # Z

    .line 116
    iget-boolean v0, p0, Lcom/nkvt/SwitchStyle;->shadowEffect:Z

    if-ne v0, p1, :cond_0

    return-void

    .line 117
    :cond_0
    iput-boolean p1, p0, Lcom/nkvt/SwitchStyle;->shadowEffect:Z

    .line 119
    const/4 v0, 0x0

    if-eqz p1, :cond_1

    .line 120
    iget-object v1, p0, Lcom/nkvt/SwitchStyle;->buttonPaint:Landroid/graphics/Paint;

    iget v2, p0, Lcom/nkvt/SwitchStyle;->shadowRadius:I

    int-to-float v2, v2

    iget v3, p0, Lcom/nkvt/SwitchStyle;->shadowOffset:I

    int-to-float v3, v3

    iget v4, p0, Lcom/nkvt/SwitchStyle;->shadowColor:I

    invoke-virtual {v1, v2, v0, v3, v4}, Landroid/graphics/Paint;->setShadowLayer(FFFI)V

    goto :goto_0

    .line 125
    :cond_1
    iget-object v1, p0, Lcom/nkvt/SwitchStyle;->buttonPaint:Landroid/graphics/Paint;

    const/4 v2, 0x0

    invoke-virtual {v1, v0, v0, v0, v2}, Landroid/graphics/Paint;->setShadowLayer(FFFI)V

    .line 130
    :goto_0
    return-void
.end method

.method public setUncheckedButtonColor(I)V
    .locals 0
    .param p1, "uncheckButtonColor"    # I

    .line 104
    iput p1, p0, Lcom/nkvt/SwitchStyle;->uncheckButtonColor:I

    .line 105
    return-void
.end method

.method public toggle()V
    .locals 1

    .line 96
    const/4 v0, 0x1

    invoke-virtual {p0, v0}, Lcom/nkvt/SwitchStyle;->toggle(Z)V

    .line 97
    return-void
.end method

.method public toggle(Z)V
    .locals 1
    .param p1, "animate"    # Z

    .line 112
    const/4 v0, 0x1

    invoke-direct {p0, p1, v0}, Lcom/nkvt/SwitchStyle;->toggle(ZZ)V

    .line 113
    return-void
.end method
