.class public final synthetic Lcom/api/-$$Lambda$KeyAuth$md10HFLIVF5PesGYhMDSifSlOG8;
.super Ljava/lang/Object;
.source "lambda"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic f$0:Lcom/api/KeyAuth;

.field public final synthetic f$1:Ljava/lang/String;

.field public final synthetic f$2:Ljava/lang/String;

.field public final synthetic f$3:Lcom/api/KeyAuth$LoginCallback;


# direct methods
.method public synthetic constructor <init>(Lcom/api/KeyAuth;Ljava/lang/String;Ljava/lang/String;Lcom/api/KeyAuth$LoginCallback;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/api/-$$Lambda$KeyAuth$md10HFLIVF5PesGYhMDSifSlOG8;->f$0:Lcom/api/KeyAuth;

    iput-object p2, p0, Lcom/api/-$$Lambda$KeyAuth$md10HFLIVF5PesGYhMDSifSlOG8;->f$1:Ljava/lang/String;

    iput-object p3, p0, Lcom/api/-$$Lambda$KeyAuth$md10HFLIVF5PesGYhMDSifSlOG8;->f$2:Ljava/lang/String;

    iput-object p4, p0, Lcom/api/-$$Lambda$KeyAuth$md10HFLIVF5PesGYhMDSifSlOG8;->f$3:Lcom/api/KeyAuth$LoginCallback;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 4

    iget-object v0, p0, Lcom/api/-$$Lambda$KeyAuth$md10HFLIVF5PesGYhMDSifSlOG8;->f$0:Lcom/api/KeyAuth;

    iget-object v1, p0, Lcom/api/-$$Lambda$KeyAuth$md10HFLIVF5PesGYhMDSifSlOG8;->f$1:Ljava/lang/String;

    iget-object v2, p0, Lcom/api/-$$Lambda$KeyAuth$md10HFLIVF5PesGYhMDSifSlOG8;->f$2:Ljava/lang/String;

    iget-object v3, p0, Lcom/api/-$$Lambda$KeyAuth$md10HFLIVF5PesGYhMDSifSlOG8;->f$3:Lcom/api/KeyAuth$LoginCallback;

    invoke-virtual {v0, v1, v2, v3}, Lcom/api/KeyAuth;->lambda$login$1$KeyAuth(Ljava/lang/String;Ljava/lang/String;Lcom/api/KeyAuth$LoginCallback;)V

    return-void
.end method
