.class final Lcom/nkvt/Menu$4;
.super Ljava/lang/Object;
.source "Menu.java"

# interfaces
.implements Landroid/widget/CompoundButton$OnCheckedChangeListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/nkvt/Menu;->addSwitch(Ljava/lang/String;I)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x8
    name = null
.end annotation


# instance fields
.field final synthetic val$ID:I

.field final synthetic val$textView:Landroid/widget/TextView;


# direct methods
.method constructor <init>(ILandroid/widget/TextView;)V
    .locals 0

    .line 327
    iput p1, p0, Lcom/nkvt/Menu$4;->val$ID:I

    iput-object p2, p0, Lcom/nkvt/Menu$4;->val$textView:Landroid/widget/TextView;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onCheckedChanged(Landroid/widget/CompoundButton;Z)V
    .locals 2
    .param p1, "buttonView"    # Landroid/widget/CompoundButton;
    .param p2, "isChecked"    # Z

    .line 330
    iget v0, p0, Lcom/nkvt/Menu$4;->val$ID:I

    const/4 v1, 0x0

    invoke-static {v0, v1}, Lcom/nkvt/Menu;->ChangesID(II)V

    .line 331
    iget-object v0, p0, Lcom/nkvt/Menu$4;->val$textView:Landroid/widget/TextView;

    if-eqz p2, :cond_0

    const/4 v1, -0x1

    goto :goto_0

    :cond_0
    const v1, -0x777778

    :goto_0
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setTextColor(I)V

    .line 332
    return-void
.end method
