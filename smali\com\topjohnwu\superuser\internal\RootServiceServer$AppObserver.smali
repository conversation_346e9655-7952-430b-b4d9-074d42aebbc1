.class Lcom/topjohnwu/superuser/internal/RootServiceServer$AppObserver;
.super Landroid/os/FileObserver;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/topjohnwu/superuser/internal/RootServiceServer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = "AppObserver"
.end annotation


# instance fields
.field private final name:Ljava/lang/String;

.field final synthetic this$0:Lcom/topjohnwu/superuser/internal/RootServiceServer;


# direct methods
.method constructor <init>(Lcom/topjohnwu/superuser/internal/RootServiceServer;Ljava/io/File;)V
    .locals 1

    iput-object p1, p0, Lcom/topjohnwu/superuser/internal/RootServiceServer$AppObserver;->this$0:Lcom/topjohnwu/superuser/internal/RootServiceServer;

    invoke-virtual {p2}, Ljava/io/File;->getParent()Ljava/lang/String;

    move-result-object p1

    const/16 v0, 0x7c0

    invoke-direct {p0, p1, v0}, Landroid/os/FileObserver;-><init>(Ljava/lang/String;I)V

    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->FDJMBᵔˈٴˆˎﾞﹳˋʼˋLfVzn()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/io/File;->getParent()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string v0, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->qAFPBᴵʽʼˊـˎٴˊˆᵔˏᵔٴʼᵎˆWRquM()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0, p1}, Lcom/topjohnwu/superuser/internal/Utils;->log(Ljava/lang/String;Ljava/lang/Object;)V

    invoke-virtual {p2}, Ljava/io/File;->getName()Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lcom/topjohnwu/superuser/internal/RootServiceServer$AppObserver;->name:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method synthetic lambda$onEvent$0$com-topjohnwu-superuser-internal-RootServiceServer$AppObserver()V
    .locals 2

    const-string v0, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->GkuSzᵎᐧˏˋˎˉˆᵔVdLfp()Ljava/lang/String;

    move-result-object v0

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->hZEmSʽˉⁱˑʿᵔˏᵔᐧʾﾞˈˊᐧᵢˑˊᵔQMtaQ()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/topjohnwu/superuser/internal/Utils;->log(Ljava/lang/String;Ljava/lang/Object;)V

    iget-object v0, p0, Lcom/topjohnwu/superuser/internal/RootServiceServer$AppObserver;->this$0:Lcom/topjohnwu/superuser/internal/RootServiceServer;

    const/4 v1, 0x1

    invoke-static {v0, v1}, Lcom/topjohnwu/superuser/internal/RootServiceServer;->access$000(Lcom/topjohnwu/superuser/internal/RootServiceServer;Z)V

    return-void
.end method

.method public onEvent(ILjava/lang/String;)V
    .locals 1

    const/16 v0, 0x400

    if-eq p1, v0, :cond_0

    iget-object p1, p0, Lcom/topjohnwu/superuser/internal/RootServiceServer$AppObserver;->name:Ljava/lang/String;

    invoke-virtual {p1, p2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_1

    :cond_0
    new-instance p1, Lcom/topjohnwu/superuser/internal/RootServiceServer$AppObserver$$ExternalSyntheticLambda0;

    invoke-direct {p1, p0}, Lcom/topjohnwu/superuser/internal/RootServiceServer$AppObserver$$ExternalSyntheticLambda0;-><init>(Lcom/topjohnwu/superuser/internal/RootServiceServer$AppObserver;)V

    invoke-static {p1}, Lcom/topjohnwu/superuser/internal/UiThreadHandler;->run(Ljava/lang/Runnable;)V

    :cond_1
    return-void
.end method
