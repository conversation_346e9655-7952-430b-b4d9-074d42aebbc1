.class public final synthetic Lcom/topjohnwu/superuser/internal/Utils$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# static fields
.field public static final synthetic INSTANCE:Lcom/topjohnwu/superuser/internal/Utils$$ExternalSyntheticLambda0;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/topjohnwu/superuser/internal/Utils$$ExternalSyntheticLambda0;

    invoke-direct {v0}, Lcom/topjohnwu/superuser/internal/Utils$$ExternalSyntheticLambda0;-><init>()V

    sput-object v0, Lcom/topjohnwu/superuser/internal/Utils$$ExternalSyntheticLambda0;->INSTANCE:Lcom/topjohnwu/superuser/internal/Utils$$ExternalSyntheticLambda0;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 0

    invoke-static {}, Lcom/topjohnwu/superuser/internal/Utils;->lambda$getContext$0()V

    return-void
.end method
