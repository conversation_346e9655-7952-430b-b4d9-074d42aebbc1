.class public Lcom/nkvt/Login;
.super Ljava/lang/Object;
.source "Login.java"


# static fields
.field private static final API_URL:Ljava/lang/String; = "https://keyauth.win/api/1.3/"

.field private static final APP_NAME:Ljava/lang/String; = "AIMKILL"

.field private static final OWNER_ID:Ljava/lang/String; = "tnktGBPFCy"

.field private static final SECRET:Ljava/lang/String; = "5e956adb41770b80cb3e83ab6cb1183d9e1704c161e649614155392f0dfcc9a1"

.field private static final VERSION:Ljava/lang/String; = "1.0"


# instance fields
.field private context:Landroid/content/Context;

.field private isDarkMode:Z

.field private loadingBar:Landroid/widget/ProgressBar;

.field private loadingText:Landroid/widget/TextView;

.field private rootContainer:Landroid/widget/LinearLayout;

.field private utils:Lcom/nkvt/Utils;


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 1
    .param p1, "context"    # Landroid/content/Context;

    .line 43
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 34
    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/nkvt/Login;->isDarkMode:Z

    .line 44
    iput-object p1, p0, Lcom/nkvt/Login;->context:Landroid/content/Context;

    .line 45
    new-instance v0, Lcom/nkvt/Utils;

    invoke-direct {v0, p1}, Lcom/nkvt/Utils;-><init>(Landroid/content/Context;)V

    iput-object v0, p0, Lcom/nkvt/Login;->utils:Lcom/nkvt/Utils;

    .line 46
    invoke-direct {p0}, Lcom/nkvt/Login;->Init()V

    .line 47
    invoke-direct {p0}, Lcom/nkvt/Login;->bypassAuth()V

    .line 48
    return-void
.end method

.method private bypassAuth()V
    .locals 3

    .line 999
    new-instance v0, Lcom/nkvt/Menu;

    iget-object v1, p0, Lcom/nkvt/Login;->context:Landroid/content/Context;

    const/4 v2, 0x1

    invoke-direct {v0, v1, v2}, Lcom/nkvt/Menu;-><init>(Landroid/content/Context;I)V

    .line 1000
    return-void
.end method

.method private Init()V
    .locals 21

    .line 50
    move-object/from16 v6, p0

    new-instance v0, Landroid/widget/LinearLayout;

    iget-object v1, v6, Lcom/nkvt/Login;->context:Landroid/content/Context;

    invoke-direct {v0, v1}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    iput-object v0, v6, Lcom/nkvt/Login;->rootContainer:Landroid/widget/LinearLayout;

    .line 51
    const/4 v7, 0x1

    invoke-virtual {v0, v7}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 52
    iget-object v0, v6, Lcom/nkvt/Login;->rootContainer:Landroid/widget/LinearLayout;

    const/16 v8, 0x11

    invoke-virtual {v0, v8}, Landroid/widget/LinearLayout;->setGravity(I)V

    .line 53
    iget-object v0, v6, Lcom/nkvt/Login;->rootContainer:Landroid/widget/LinearLayout;

    const/high16 v9, -0x1000000

    invoke-virtual {v0, v9}, Landroid/widget/LinearLayout;->setBackgroundColor(I)V

    .line 55
    new-instance v0, Landroid/widget/LinearLayout;

    iget-object v1, v6, Lcom/nkvt/Login;->context:Landroid/content/Context;

    invoke-direct {v0, v1}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    move-object v10, v0

    .line 56
    .local v10, "card":Landroid/widget/LinearLayout;
    invoke-virtual {v10, v7}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 57
    invoke-virtual {v10, v7}, Landroid/widget/LinearLayout;->setGravity(I)V

    .line 58
    new-instance v0, Landroid/widget/LinearLayout$LayoutParams;

    const/16 v1, 0x320

    const/16 v2, 0x258

    invoke-direct {v0, v1, v2}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v10, v0}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 59
    const/16 v11, 0x28

    invoke-virtual {v10, v11, v11, v11, v11}, Landroid/widget/LinearLayout;->setPadding(IIII)V

    .line 61
    new-instance v0, Landroid/graphics/drawable/GradientDrawable;

    invoke-direct {v0}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    move-object v12, v0

    .line 62
    .local v12, "cardBg":Landroid/graphics/drawable/GradientDrawable;
    const/4 v13, -0x1

    invoke-virtual {v12, v13}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 63
    const/high16 v0, 0x42200000    # 40.0f

    invoke-virtual {v12, v0}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 64
    const/4 v0, 0x6

    const/high16 v14, -0x10000

    invoke-virtual {v12, v0, v14}, Landroid/graphics/drawable/GradientDrawable;->setStroke(II)V

    .line 65
    invoke-virtual {v10, v12}, Landroid/widget/LinearLayout;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 66
    const/high16 v15, 0x41a00000    # 20.0f

    invoke-virtual {v10, v15}, Landroid/widget/LinearLayout;->setElevation(F)V

    .line 67
    invoke-virtual {v10, v15}, Landroid/widget/LinearLayout;->setTranslationZ(F)V

    .line 70
    new-instance v0, Landroid/widget/EditText;

    iget-object v1, v6, Lcom/nkvt/Login;->context:Landroid/content/Context;

    invoke-direct {v0, v1}, Landroid/widget/EditText;-><init>(Landroid/content/Context;)V

    move-object v5, v0

    .line 71
    .local v5, "inputLicense":Landroid/widget/EditText;
    new-instance v0, Landroid/widget/TextView;

    iget-object v1, v6, Lcom/nkvt/Login;->context:Landroid/content/Context;

    invoke-direct {v0, v1}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    move-object v4, v0

    .line 72
    .local v4, "title":Landroid/widget/TextView;
    new-instance v0, Landroid/widget/TextView;

    iget-object v1, v6, Lcom/nkvt/Login;->context:Landroid/content/Context;

    invoke-direct {v0, v1}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    move-object v3, v0

    .line 74
    .local v3, "subtitle":Landroid/widget/TextView;
    new-instance v0, Landroid/widget/LinearLayout;

    iget-object v1, v6, Lcom/nkvt/Login;->context:Landroid/content/Context;

    invoke-direct {v0, v1}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    move-object v2, v0

    .line 75
    .local v2, "topRow":Landroid/widget/LinearLayout;
    const/4 v1, 0x0

    invoke-virtual {v2, v1}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 76
    new-instance v0, Landroid/widget/LinearLayout$LayoutParams;

    const/4 v11, -0x2

    invoke-direct {v0, v13, v11}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v2, v0}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 78
    const v0, 0x800005

    invoke-virtual {v2, v0}, Landroid/widget/LinearLayout;->setGravity(I)V

    .line 80
    new-instance v0, Landroid/widget/Switch;

    iget-object v11, v6, Lcom/nkvt/Login;->context:Landroid/content/Context;

    invoke-direct {v0, v11}, Landroid/widget/Switch;-><init>(Landroid/content/Context;)V

    move-object v11, v0

    .line 81
    .local v11, "modeToggle":Landroid/widget/Switch;
    const-string v0, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->ephPfٴᵢﹶˏʼᴵˉﹶˆⁱʼˋⁱٴᵢיiUXSA()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v11, v0}, Landroid/widget/Switch;->setTextOn(Ljava/lang/CharSequence;)V

    .line 82
    const-string v0, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->QXMWHˎˈٴˆᴵᐧˈⁱʿˏﾞʾˋˊﹳˎˈYNFHZ()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v11, v0}, Landroid/widget/Switch;->setTextOff(Ljava/lang/CharSequence;)V

    .line 83
    invoke-virtual {v11, v1}, Landroid/widget/Switch;->setChecked(Z)V

    .line 84
    const-string v0, ""

    invoke-virtual {v11, v0}, Landroid/widget/Switch;->setText(Ljava/lang/CharSequence;)V

    .line 85
    const/16 v15, 0xa

    invoke-virtual {v11, v1, v1, v1, v15}, Landroid/widget/Switch;->setPadding(IIII)V

    .line 86
    new-instance v9, Lcom/nkvt/-$$Lambda$Login$RQAEVdlQGFU1KYlycvS4q-MK1Mc;

    move-object/from16 v17, v0

    move-object v0, v9

    const/4 v13, 0x0

    move-object/from16 v1, p0

    move-object v13, v2

    .end local v2    # "topRow":Landroid/widget/LinearLayout;
    .local v13, "topRow":Landroid/widget/LinearLayout;
    move-object v2, v10

    move-object/from16 v18, v3

    .end local v3    # "subtitle":Landroid/widget/TextView;
    .local v18, "subtitle":Landroid/widget/TextView;
    move-object v3, v5

    move-object/from16 v19, v4

    .end local v4    # "title":Landroid/widget/TextView;
    .local v19, "title":Landroid/widget/TextView;
    move-object/from16 v20, v5

    .end local v5    # "inputLicense":Landroid/widget/EditText;
    .local v20, "inputLicense":Landroid/widget/EditText;
    move-object/from16 v5, v18

    invoke-direct/range {v0 .. v5}, Lcom/nkvt/-$$Lambda$Login$RQAEVdlQGFU1KYlycvS4q-MK1Mc;-><init>(Lcom/nkvt/Login;Landroid/widget/LinearLayout;Landroid/widget/EditText;Landroid/widget/TextView;Landroid/widget/TextView;)V

    invoke-virtual {v11, v9}, Landroid/widget/Switch;->setOnCheckedChangeListener(Landroid/widget/CompoundButton$OnCheckedChangeListener;)V

    .line 89
    invoke-virtual {v13, v11}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 90
    invoke-virtual {v10, v13}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 92
    const-string v0, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->EpgRIʿʻʽˉʽⁱᴵᐧﾞˋˊʿIezcx()Ljava/lang/String;

    move-result-object v0

    move-object/from16 v1, v19

    .end local v19    # "title":Landroid/widget/TextView;
    .local v1, "title":Landroid/widget/TextView;
    invoke-virtual {v1, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 93
    const/high16 v0, 0x41c00000    # 24.0f

    invoke-virtual {v1, v0}, Landroid/widget/TextView;->setTextSize(F)V

    .line 94
    invoke-virtual {v1, v14}, Landroid/widget/TextView;->setTextColor(I)V

    .line 95
    const/4 v0, 0x0

    invoke-virtual {v1, v0, v7}, Landroid/widget/TextView;->setTypeface(Landroid/graphics/Typeface;I)V

    .line 96
    const/high16 v0, 0x41200000    # 10.0f

    const/4 v2, 0x0

    invoke-virtual {v1, v0, v2, v2, v14}, Landroid/widget/TextView;->setShadowLayer(FFFI)V

    .line 97
    invoke-virtual {v1, v8}, Landroid/widget/TextView;->setGravity(I)V

    .line 98
    invoke-virtual {v10, v1}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 100
    const-string v0, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->tbrllˎᵢˊˎˑˋˉˊיˋٴᵔﹶˆˎٴmxVWs()Ljava/lang/String;

    move-result-object v0

    move-object/from16 v2, v18

    .end local v18    # "subtitle":Landroid/widget/TextView;
    .local v2, "subtitle":Landroid/widget/TextView;
    invoke-virtual {v2, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 101
    const/high16 v0, 0x41600000    # 14.0f

    invoke-virtual {v2, v0}, Landroid/widget/TextView;->setTextSize(F)V

    .line 102
    const v3, -0x777778

    invoke-virtual {v2, v3}, Landroid/widget/TextView;->setTextColor(I)V

    .line 103
    invoke-virtual {v2, v8}, Landroid/widget/TextView;->setGravity(I)V

    .line 104
    const/4 v4, 0x0

    invoke-virtual {v2, v4, v15, v4, v4}, Landroid/widget/TextView;->setPadding(IIII)V

    .line 105
    invoke-virtual {v10, v2}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 107
    new-instance v4, Landroid/view/View;

    iget-object v5, v6, Lcom/nkvt/Login;->context:Landroid/content/Context;

    invoke-direct {v4, v5}, Landroid/view/View;-><init>(Landroid/content/Context;)V

    .line 108
    .local v4, "spacer":Landroid/view/View;
    new-instance v5, Landroid/widget/LinearLayout$LayoutParams;

    const/16 v7, 0x64

    const/4 v9, -0x1

    invoke-direct {v5, v9, v7}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v4, v5}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 110
    invoke-virtual {v10, v4}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 112
    const-string v5, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->yxbjzˏˆˊᐧʾʻʼﾞʻˉˋﾞʻˆʽʼˎᵎXMhFT()Ljava/lang/String;

    move-result-object v5

    move-object/from16 v7, v20

    .end local v20    # "inputLicense":Landroid/widget/EditText;
    .local v7, "inputLicense":Landroid/widget/EditText;
    invoke-virtual {v7, v5}, Landroid/widget/EditText;->setHint(Ljava/lang/CharSequence;)V

    .line 113
    const/high16 v5, 0x41800000    # 16.0f

    invoke-virtual {v7, v5}, Landroid/widget/EditText;->setTextSize(F)V

    .line 114
    const/high16 v5, -0x1000000

    invoke-virtual {v7, v5}, Landroid/widget/EditText;->setTextColor(I)V

    .line 115
    const/16 v5, 0x1e

    const/16 v9, 0x19

    invoke-virtual {v7, v5, v9, v5, v9}, Landroid/widget/EditText;->setPadding(IIII)V

    .line 116
    invoke-virtual {v7, v8}, Landroid/widget/EditText;->setGravity(I)V

    .line 118
    new-instance v9, Landroid/graphics/drawable/GradientDrawable;

    invoke-direct {v9}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    .line 119
    .local v9, "inputBg":Landroid/graphics/drawable/GradientDrawable;
    const/high16 v3, 0x41a00000    # 20.0f

    invoke-virtual {v9, v3}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 120
    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->AdQyWˊˉﹶʻʿﾞʼﹶˉᵢʿᵔˈᵎʿRQEpb()Ljava/lang/String;

    move-result-object v3

    invoke-static {v3}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v3

    invoke-virtual {v9, v3}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 121
    invoke-virtual {v7, v9}, Landroid/widget/EditText;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 122
    invoke-virtual {v10, v7}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 124
    iget-object v3, v6, Lcom/nkvt/Login;->context:Landroid/content/Context;

    const-string v0, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->FAiutᐧﹳﾞـʼʻﾞʼᵔᴵˋⁱـˎODXnN()Ljava/lang/String;

    move-result-object v0

    const/4 v8, 0x0

    invoke-virtual {v3, v0, v8}, Landroid/content/Context;->getSharedPreferences(Ljava/lang/String;I)Landroid/content/SharedPreferences;

    move-result-object v0

    .line 125
    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->fhHqoʻᵢˋⁱـⁱⁱⁱⁱʽﾞᵎʽˋˊˋʽˈDCfaA()Ljava/lang/String;

    move-result-object v3

    move-object/from16 v8, v17

    invoke-interface {v0, v3, v8}, Landroid/content/SharedPreferences;->getString(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 126
    .local v0, "savedKey":Ljava/lang/String;
    invoke-virtual {v7, v0}, Landroid/widget/EditText;->setText(Ljava/lang/CharSequence;)V

    .line 128
    new-instance v3, Landroid/widget/Button;

    iget-object v8, v6, Lcom/nkvt/Login;->context:Landroid/content/Context;

    invoke-direct {v3, v8}, Landroid/widget/Button;-><init>(Landroid/content/Context;)V

    .line 129
    .local v3, "loginButton":Landroid/widget/Button;
    const-string v8, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->iFGUzᵢⁱˏʽˋᵎUgXQM()Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v3, v8}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    .line 130
    const/4 v8, -0x1

    invoke-virtual {v3, v8}, Landroid/widget/Button;->setTextColor(I)V

    .line 131
    const/high16 v8, 0x41900000    # 18.0f

    invoke-virtual {v3, v8}, Landroid/widget/Button;->setTextSize(F)V

    .line 132
    invoke-virtual {v3, v5, v5, v5, v5}, Landroid/widget/Button;->setPadding(IIII)V

    .line 134
    new-instance v5, Landroid/graphics/drawable/GradientDrawable;

    invoke-direct {v5}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    .line 135
    .local v5, "btnBg":Landroid/graphics/drawable/GradientDrawable;
    invoke-virtual {v5, v14}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 136
    const/high16 v8, 0x42c80000    # 100.0f

    invoke-virtual {v5, v8}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 137
    const-string v8, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->yaaNbʻˎﹳᐧˊٴﹳˋיﹳʽⁱʾᵢˈʾNuHvj()Ljava/lang/String;

    move-result-object v8

    invoke-static {v8}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v8

    const/4 v14, 0x4

    invoke-virtual {v5, v14, v8}, Landroid/graphics/drawable/GradientDrawable;->setStroke(II)V

    .line 138
    invoke-virtual {v3, v5}, Landroid/widget/Button;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 140
    new-instance v8, Landroid/widget/LinearLayout$LayoutParams;

    const/4 v14, -0x2

    const/4 v15, -0x1

    invoke-direct {v8, v15, v14}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    .line 142
    .local v8, "btnParams":Landroid/widget/LinearLayout$LayoutParams;
    move-object/from16 v16, v0

    const/4 v0, 0x0

    const/16 v14, 0xa

    const/16 v15, 0x28

    .end local v0    # "savedKey":Ljava/lang/String;
    .local v16, "savedKey":Ljava/lang/String;
    invoke-virtual {v8, v0, v15, v0, v14}, Landroid/widget/LinearLayout$LayoutParams;->setMargins(IIII)V

    .line 143
    invoke-virtual {v3, v8}, Landroid/widget/Button;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 144
    invoke-virtual {v10, v3}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 146
    new-instance v14, Landroid/widget/LinearLayout;

    iget-object v15, v6, Lcom/nkvt/Login;->context:Landroid/content/Context;

    invoke-direct {v14, v15}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    .line 147
    .local v14, "loadingLayout":Landroid/widget/LinearLayout;
    invoke-virtual {v14, v0}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 148
    const/16 v15, 0x11

    invoke-virtual {v14, v15}, Landroid/widget/LinearLayout;->setGravity(I)V

    .line 149
    const/16 v15, 0xa

    invoke-virtual {v14, v0, v15, v0, v15}, Landroid/widget/LinearLayout;->setPadding(IIII)V

    .line 151
    new-instance v0, Landroid/widget/ProgressBar;

    iget-object v15, v6, Lcom/nkvt/Login;->context:Landroid/content/Context;

    invoke-direct {v0, v15}, Landroid/widget/ProgressBar;-><init>(Landroid/content/Context;)V

    iput-object v0, v6, Lcom/nkvt/Login;->loadingBar:Landroid/widget/ProgressBar;

    .line 152
    const/16 v15, 0x8

    invoke-virtual {v0, v15}, Landroid/widget/ProgressBar;->setVisibility(I)V

    .line 153
    iget-object v0, v6, Lcom/nkvt/Login;->loadingBar:Landroid/widget/ProgressBar;

    invoke-virtual {v0}, Landroid/widget/ProgressBar;->getIndeterminateDrawable()Landroid/graphics/drawable/Drawable;

    move-result-object v0

    sget-object v15, Landroid/graphics/PorterDuff$Mode;->SRC_IN:Landroid/graphics/PorterDuff$Mode;

    const/high16 v1, -0x10000

    .end local v1    # "title":Landroid/widget/TextView;
    .restart local v19    # "title":Landroid/widget/TextView;
    invoke-virtual {v0, v1, v15}, Landroid/graphics/drawable/Drawable;->setColorFilter(ILandroid/graphics/PorterDuff$Mode;)V

    .line 155
    new-instance v0, Landroid/widget/TextView;

    iget-object v15, v6, Lcom/nkvt/Login;->context:Landroid/content/Context;

    invoke-direct {v0, v15}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    iput-object v0, v6, Lcom/nkvt/Login;->loadingText:Landroid/widget/TextView;

    .line 156
    const-string v15, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->jSXOGʾᵢʼﾞˆᴵʿʻˉʻˎˑﾞᵢˋˎVkLSg()Ljava/lang/String;

    move-result-object v15

    invoke-virtual {v0, v15}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 157
    iget-object v0, v6, Lcom/nkvt/Login;->loadingText:Landroid/widget/TextView;

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setTextColor(I)V

    .line 158
    iget-object v0, v6, Lcom/nkvt/Login;->loadingText:Landroid/widget/TextView;

    const/high16 v1, 0x41600000    # 14.0f

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setTextSize(F)V

    .line 159
    iget-object v0, v6, Lcom/nkvt/Login;->loadingText:Landroid/widget/TextView;

    const/16 v1, 0x14

    const/4 v15, 0x0

    invoke-virtual {v0, v1, v15, v15, v15}, Landroid/widget/TextView;->setPadding(IIII)V

    .line 160
    iget-object v0, v6, Lcom/nkvt/Login;->loadingText:Landroid/widget/TextView;

    const/16 v15, 0x8

    invoke-virtual {v0, v15}, Landroid/widget/TextView;->setVisibility(I)V

    .line 162
    iget-object v0, v6, Lcom/nkvt/Login;->loadingBar:Landroid/widget/ProgressBar;

    invoke-virtual {v14, v0}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 163
    iget-object v0, v6, Lcom/nkvt/Login;->loadingText:Landroid/widget/TextView;

    invoke-virtual {v14, v0}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 164
    invoke-virtual {v10, v14}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 166
    new-instance v0, Landroid/widget/TextView;

    iget-object v15, v6, Lcom/nkvt/Login;->context:Landroid/content/Context;

    invoke-direct {v0, v15}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    .line 167
    .local v0, "footer":Landroid/widget/TextView;
    const-string v15, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->pdtQmʻˑʼᵢⁱˑﹶʼʾᵎJzvPK()Ljava/lang/String;

    move-result-object v15

    invoke-virtual {v0, v15}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 168
    const/high16 v15, 0x41400000    # 12.0f

    invoke-virtual {v0, v15}, Landroid/widget/TextView;->setTextSize(F)V

    .line 169
    const v15, -0x777778

    invoke-virtual {v0, v15}, Landroid/widget/TextView;->setTextColor(I)V

    .line 170
    const/16 v15, 0x11

    invoke-virtual {v0, v15}, Landroid/widget/TextView;->setGravity(I)V

    .line 171
    const/4 v15, 0x0

    invoke-virtual {v0, v15, v1, v15, v15}, Landroid/widget/TextView;->setPadding(IIII)V

    .line 172
    invoke-virtual {v10, v0}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 174
    iget-object v1, v6, Lcom/nkvt/Login;->rootContainer:Landroid/widget/LinearLayout;

    invoke-virtual {v1, v10}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 175
    iget-object v1, v6, Lcom/nkvt/Login;->context:Landroid/content/Context;

    check-cast v1, Landroid/app/Activity;

    iget-object v15, v6, Lcom/nkvt/Login;->rootContainer:Landroid/widget/LinearLayout;

    invoke-virtual {v1, v15}, Landroid/app/Activity;->setContentView(Landroid/view/View;)V

    .line 177
    new-instance v1, Lcom/nkvt/-$$Lambda$Login$rcAkLK4T-RYk-MhJTijPApkNF0k;

    invoke-direct {v1, v6, v7, v3}, Lcom/nkvt/-$$Lambda$Login$rcAkLK4T-RYk-MhJTijPApkNF0k;-><init>(Lcom/nkvt/Login;Landroid/widget/EditText;Landroid/widget/Button;)V

    invoke-virtual {v3, v1}, Landroid/widget/Button;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 222
    return-void
.end method

.method private getHWID()Ljava/lang/String;
    .locals 2

    .line 272
    iget-object v0, p0, Lcom/nkvt/Login;->context:Landroid/content/Context;

    invoke-virtual {v0}, Landroid/content/Context;->getContentResolver()Landroid/content/ContentResolver;

    move-result-object v0

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->AKyHuˋʾﹳﾞˑˑᴵﹳʾˋᵔʿRXCof()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Landroid/provider/Settings$Secure;->getString(Landroid/content/ContentResolver;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method private postError(Ljava/lang/String;Landroid/widget/Button;)V
    .locals 2
    .param p1, "message"    # Ljava/lang/String;
    .param p2, "loginButton"    # Landroid/widget/Button;

    .line 258
    new-instance v0, Landroid/os/Handler;

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v1

    invoke-direct {v0, v1}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    new-instance v1, Lcom/nkvt/-$$Lambda$Login$TKfCucj51li1-uMY6nySdGCXS8w;

    invoke-direct {v1, p0, p1, p2}, Lcom/nkvt/-$$Lambda$Login$TKfCucj51li1-uMY6nySdGCXS8w;-><init>(Lcom/nkvt/Login;Ljava/lang/String;Landroid/widget/Button;)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    .line 264
    return-void
.end method

.method private sendRequest(Ljava/lang/String;)Lorg/json/JSONObject;
    .locals 6
    .param p1, "urlString"    # Ljava/lang/String;
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    .line 246
    new-instance v0, Ljava/net/URL;

    invoke-direct {v0, p1}, Ljava/net/URL;-><init>(Ljava/lang/String;)V

    invoke-virtual {v0}, Ljava/net/URL;->openConnection()Ljava/net/URLConnection;

    move-result-object v0

    check-cast v0, Ljava/net/HttpURLConnection;

    .line 247
    .local v0, "conn":Ljava/net/HttpURLConnection;
    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->MiQhXﹶˏﹳʻיˋיʼˈٴˑיᴵˉᴵᐧˋʾᴵﹶـˋLCNSA()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/net/HttpURLConnection;->setRequestMethod(Ljava/lang/String;)V

    .line 249
    new-instance v1, Ljava/io/BufferedReader;

    new-instance v2, Ljava/io/InputStreamReader;

    invoke-virtual {v0}, Ljava/net/HttpURLConnection;->getInputStream()Ljava/io/InputStream;

    move-result-object v3

    invoke-direct {v2, v3}, Ljava/io/InputStreamReader;-><init>(Ljava/io/InputStream;)V

    invoke-direct {v1, v2}, Ljava/io/BufferedReader;-><init>(Ljava/io/Reader;)V

    .line 250
    .local v1, "reader":Ljava/io/BufferedReader;
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 252
    .local v2, "response":Ljava/lang/StringBuilder;
    :goto_0
    invoke-virtual {v1}, Ljava/io/BufferedReader;->readLine()Ljava/lang/String;

    move-result-object v3

    move-object v4, v3

    .local v4, "line":Ljava/lang/String;
    if-eqz v3, :cond_0

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 253
    :cond_0
    invoke-virtual {v1}, Ljava/io/BufferedReader;->close()V

    .line 254
    new-instance v3, Lorg/json/JSONObject;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-direct {v3, v5}, Lorg/json/JSONObject;-><init>(Ljava/lang/String;)V

    return-object v3
.end method

.method private showToast(Ljava/lang/String;)V
    .locals 2
    .param p1, "message"    # Ljava/lang/String;

    .line 267
    new-instance v0, Landroid/os/Handler;

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v1

    invoke-direct {v0, v1}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    new-instance v1, Lcom/nkvt/-$$Lambda$Login$0W2MsUS3jkOqTZbWxqtBXup1__g;

    invoke-direct {v1, p0, p1}, Lcom/nkvt/-$$Lambda$Login$0W2MsUS3jkOqTZbWxqtBXup1__g;-><init>(Lcom/nkvt/Login;Ljava/lang/String;)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    .line 269
    return-void
.end method

.method private toggleDarkMode(Landroid/view/View;Landroid/widget/EditText;Landroid/widget/TextView;Landroid/widget/TextView;)V
    .locals 5
    .param p1, "card"    # Landroid/view/View;
    .param p2, "inputLicense"    # Landroid/widget/EditText;
    .param p3, "title"    # Landroid/widget/TextView;
    .param p4, "subtitle"    # Landroid/widget/TextView;

    .line 225
    iget-boolean v0, p0, Lcom/nkvt/Login;->isDarkMode:Z

    xor-int/lit8 v0, v0, 0x1

    iput-boolean v0, p0, Lcom/nkvt/Login;->isDarkMode:Z

    .line 226
    const v1, -0x777778

    const/high16 v2, -0x10000

    const/4 v3, -0x1

    if-eqz v0, :cond_0

    .line 227
    iget-object v0, p0, Lcom/nkvt/Login;->rootContainer:Landroid/widget/LinearLayout;

    const-string v4, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->OoWUGˑᐧᵎᐧʿʼᴵʾᴵˈﹳיˉˏˏʼˈʻˊˋjKMem()Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v4

    invoke-virtual {v0, v4}, Landroid/widget/LinearLayout;->setBackgroundColor(I)V

    .line 228
    invoke-virtual {p1}, Landroid/view/View;->getBackground()Landroid/graphics/drawable/Drawable;

    move-result-object v0

    check-cast v0, Landroid/graphics/drawable/GradientDrawable;

    const-string v4, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->hsjOgⁱʻʻﹳﾞﹶʾיʽˊˑˈˑᐧٴCCPNr()Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v4

    invoke-virtual {v0, v4}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 229
    invoke-virtual {p2, v3}, Landroid/widget/EditText;->setTextColor(I)V

    .line 230
    invoke-virtual {p2, v1}, Landroid/widget/EditText;->setHintTextColor(I)V

    .line 231
    invoke-virtual {p2}, Landroid/widget/EditText;->getBackground()Landroid/graphics/drawable/Drawable;

    move-result-object v0

    check-cast v0, Landroid/graphics/drawable/GradientDrawable;

    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->LOarGᵢٴˉʿᵔˎﾞʻˑˎⁱʼˎˈˊʻˏˏˑﹳˋumKWn()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v1

    invoke-virtual {v0, v1}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 232
    invoke-virtual {p3, v2}, Landroid/widget/TextView;->setTextColor(I)V

    .line 233
    const v0, -0x333334

    invoke-virtual {p4, v0}, Landroid/widget/TextView;->setTextColor(I)V

    goto :goto_0

    .line 235
    :cond_0
    iget-object v0, p0, Lcom/nkvt/Login;->rootContainer:Landroid/widget/LinearLayout;

    const/high16 v4, -0x1000000

    invoke-virtual {v0, v4}, Landroid/widget/LinearLayout;->setBackgroundColor(I)V

    .line 236
    invoke-virtual {p1}, Landroid/view/View;->getBackground()Landroid/graphics/drawable/Drawable;

    move-result-object v0

    check-cast v0, Landroid/graphics/drawable/GradientDrawable;

    invoke-virtual {v0, v3}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 237
    invoke-virtual {p2, v4}, Landroid/widget/EditText;->setTextColor(I)V

    .line 238
    const v0, -0xbbbbbc

    invoke-virtual {p2, v0}, Landroid/widget/EditText;->setHintTextColor(I)V

    .line 239
    invoke-virtual {p2}, Landroid/widget/EditText;->getBackground()Landroid/graphics/drawable/Drawable;

    move-result-object v0

    check-cast v0, Landroid/graphics/drawable/GradientDrawable;

    const-string v3, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->fbUzmᵢᐧـﹳᵔˎיˉˊᵎᵢᴵᵎˆˈʼˆʻLuOhJ()Ljava/lang/String;

    move-result-object v3

    invoke-static {v3}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v3

    invoke-virtual {v0, v3}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 240
    invoke-virtual {p3, v2}, Landroid/widget/TextView;->setTextColor(I)V

    .line 241
    invoke-virtual {p4, v1}, Landroid/widget/TextView;->setTextColor(I)V

    .line 243
    :goto_0
    return-void
.end method


# virtual methods
.method public synthetic lambda$Init$0$Login(Landroid/widget/LinearLayout;Landroid/widget/EditText;Landroid/widget/TextView;Landroid/widget/TextView;Landroid/widget/CompoundButton;Z)V
    .locals 0
    .param p1, "card"    # Landroid/widget/LinearLayout;
    .param p2, "inputLicense"    # Landroid/widget/EditText;
    .param p3, "title"    # Landroid/widget/TextView;
    .param p4, "subtitle"    # Landroid/widget/TextView;
    .param p5, "buttonView"    # Landroid/widget/CompoundButton;
    .param p6, "isChecked"    # Z

    .line 87
    invoke-direct {p0, p1, p2, p3, p4}, Lcom/nkvt/Login;->toggleDarkMode(Landroid/view/View;Landroid/widget/EditText;Landroid/widget/TextView;Landroid/widget/TextView;)V

    return-void
.end method

.method public synthetic lambda$Init$3$Login(Landroid/widget/EditText;Landroid/widget/Button;Landroid/view/View;)V
    .locals 3
    .param p1, "inputLicense"    # Landroid/widget/EditText;
    .param p2, "loginButton"    # Landroid/widget/Button;
    .param p3, "v"    # Landroid/view/View;

    .line 178
    invoke-virtual {p1}, Landroid/widget/EditText;->getText()Landroid/text/Editable;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/String;->trim()Ljava/lang/String;

    move-result-object v0

    .line 179
    .local v0, "licenseKey":Ljava/lang/String;
    invoke-virtual {v0}, Ljava/lang/String;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_0

    .line 180
    const-string v1, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->uKvleᵢˊᴵˑˆﹳٴˊᵎᵢᵢⁱיـNULZT()Ljava/lang/String;

    move-result-object v1

    invoke-direct {p0, v1}, Lcom/nkvt/Login;->showToast(Ljava/lang/String;)V

    .line 181
    return-void

    .line 184
    :cond_0
    const/4 v1, 0x0

    invoke-virtual {p2, v1}, Landroid/widget/Button;->setEnabled(Z)V

    .line 185
    iget-object v2, p0, Lcom/nkvt/Login;->loadingBar:Landroid/widget/ProgressBar;

    invoke-virtual {v2, v1}, Landroid/widget/ProgressBar;->setVisibility(I)V

    .line 186
    iget-object v2, p0, Lcom/nkvt/Login;->loadingText:Landroid/widget/TextView;

    invoke-virtual {v2, v1}, Landroid/widget/TextView;->setVisibility(I)V

    .line 188
    new-instance v1, Ljava/lang/Thread;

    new-instance v2, Lcom/nkvt/-$$Lambda$Login$nfeBY1zY7EDhDNr0S1ddw-Wp8V8;

    invoke-direct {v2, p0, p2, v0}, Lcom/nkvt/-$$Lambda$Login$nfeBY1zY7EDhDNr0S1ddw-Wp8V8;-><init>(Lcom/nkvt/Login;Landroid/widget/Button;Ljava/lang/String;)V

    invoke-direct {v1, v2}, Ljava/lang/Thread;-><init>(Ljava/lang/Runnable;)V

    .line 220
    invoke-virtual {v1}, Ljava/lang/Thread;->start()V

    .line 221
    return-void
.end method

.method public synthetic lambda$null$1$Login()V
    .locals 3

    .line 208
    const-string v0, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->QrvTcٴʻˏˈˋיﹳʼᴵⁱˎﹶᐧʿqIKde()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v0}, Lcom/nkvt/Login;->showToast(Ljava/lang/String;)V

    .line 209
    iget-object v0, p0, Lcom/nkvt/Login;->loadingBar:Landroid/widget/ProgressBar;

    const/16 v1, 0x8

    invoke-virtual {v0, v1}, Landroid/widget/ProgressBar;->setVisibility(I)V

    .line 210
    iget-object v0, p0, Lcom/nkvt/Login;->loadingText:Landroid/widget/TextView;

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setVisibility(I)V

    .line 211
    new-instance v0, Lcom/nkvt/Menu;

    iget-object v1, p0, Lcom/nkvt/Login;->context:Landroid/content/Context;

    const/4 v2, 0x1

    invoke-direct {v0, v1, v2}, Lcom/nkvt/Menu;-><init>(Landroid/content/Context;I)V

    .line 212
    return-void
.end method

.method public synthetic lambda$null$2$Login(Landroid/widget/Button;Ljava/lang/String;)V
    .locals 8
    .param p1, "loginButton"    # Landroid/widget/Button;
    .param p2, "licenseKey"    # Ljava/lang/String;

    .line 190
    const-string v0, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->WFjzaـʼـʼˉʽﹶᐧᐧˏᴵـʿˊﾞˑᵢᵢrOvUQ()Ljava/lang/String;

    move-result-object v0

    :try_start_0
    invoke-direct {p0}, Lcom/nkvt/Login;->getHWID()Ljava/lang/String;

    move-result-object v1

    .line 191
    .local v1, "hwid":Ljava/lang/String;
    const-string v2, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->RKfPeﹶʼˆﹶﹳᵔˊⁱᐧʻⁱʽQlpGQ()Ljava/lang/String;

    move-result-object v2

    .line 192
    .local v2, "initUrl":Ljava/lang/String;
    invoke-direct {p0, v2}, Lcom/nkvt/Login;->sendRequest(Ljava/lang/String;)Lorg/json/JSONObject;

    move-result-object v3

    .line 193
    .local v3, "initRes":Lorg/json/JSONObject;
    invoke-virtual {v3, v0}, Lorg/json/JSONObject;->getBoolean(Ljava/lang/String;)Z

    move-result v4
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    const-string v5, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->wRaigـﹶʻʿʽﾞᵢˊﹳـﹶˆـᴵﹶﹳvyJLz()Ljava/lang/String;

    move-result-object v5

    if-eqz v4, :cond_0

    .line 194
    :try_start_1
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->CMKJgʼᵢʻˋᵔʾᴵʼᵎᵢˉˑʾᵔٴˑNNjby()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v5}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v0, p1}, Lcom/nkvt/Login;->postError(Ljava/lang/String;Landroid/widget/Button;)V

    .line 195
    return-void

    .line 198
    :cond_0
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->EOboxᵢﾞʼᴵʼˆᵢˎʾᴵﾞᵢᵎʿˈﹶʾʿˑᵎcSxiw()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v6, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->oCHPLﾞיʼᵎᵎˆʻˎᐧⁱˈˋˉˊsHVRX()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v6, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->gmTFAˏˆⁱﾞᵎˑᵢˈʾˊʾﹶˊʽיˈMrLsF()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v6, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->qqcioﹶˋʾˏˉʽˈˊᵔﹶﹳʻˎⁱיٴRDgsN()Ljava/lang/String;

    move-result-object v6

    .line 199
    invoke-virtual {v3, v6}, Lorg/json/JSONObject;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v6, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->wBjZAʿיˉיˈᵎˊﾞﾞʽʾٴʻᐧـˏˋiCKwc()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v6, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->OpzOIʿˏᵔʿﾞʼʾיʾˉʿˉᵢGSxTX()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v6, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->DMAzZⁱᵎᵎᴵـⁱʾᵔⁱcltxT()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v6, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->fCRfuʻיᴵˏˑᵔﾞʻʼʽˊᴵʾˉVszzx()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v6, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->MserwʻᵎˆٴⁱᵔʻٴʼˉʾʾᴵʽˊᵢˋـPGvZD()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v6, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->tQppnᴵˎיʿʿᴵPSUoq()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    .line 201
    .local v4, "loginUrl":Ljava/lang/String;
    invoke-direct {p0, v4}, Lcom/nkvt/Login;->sendRequest(Ljava/lang/String;)Lorg/json/JSONObject;

    move-result-object v6

    .line 203
    .local v6, "loginRes":Lorg/json/JSONObject;
    invoke-virtual {v6, v0}, Lorg/json/JSONObject;->getBoolean(Ljava/lang/String;)Z

    move-result v0

    if-nez v0, :cond_1

    .line 204
    iget-object v0, p0, Lcom/nkvt/Login;->context:Landroid/content/Context;

    const-string v5, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->lmPXCˋˆˎⁱᴵﹳᐧٴᵢʼˊᵢˑﾞﹶʼˋˋggHFG()Ljava/lang/String;

    move-result-object v5

    const/4 v7, 0x0

    invoke-virtual {v0, v5, v7}, Landroid/content/Context;->getSharedPreferences(Ljava/lang/String;I)Landroid/content/SharedPreferences;

    move-result-object v0

    .line 205
    invoke-interface {v0}, Landroid/content/SharedPreferences;->edit()Landroid/content/SharedPreferences$Editor;

    move-result-object v0

    const-string v5, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->PVCVyʽˋˋﹳˊʼˋᵢˊـٴᐧhcDwN()Ljava/lang/String;

    move-result-object v5

    invoke-interface {v0, v5, p2}, Landroid/content/SharedPreferences$Editor;->putString(Ljava/lang/String;Ljava/lang/String;)Landroid/content/SharedPreferences$Editor;

    move-result-object v0

    invoke-interface {v0}, Landroid/content/SharedPreferences$Editor;->apply()V

    .line 207
    new-instance v0, Landroid/os/Handler;

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v5

    invoke-direct {v0, v5}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    new-instance v5, Lcom/nkvt/-$$Lambda$Login$2RwmG7oymzyLM9eRoTbzwYJCzjE;

    invoke-direct {v5, p0}, Lcom/nkvt/-$$Lambda$Login$2RwmG7oymzyLM9eRoTbzwYJCzjE;-><init>(Lcom/nkvt/Login;)V

    invoke-virtual {v0, v5}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    goto :goto_0

    .line 214
    :cond_1
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v7, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->tIVrmˈˊـˉᴵˋˋᵢᴵᵢﹳˋˊⁱˆˉʿupffs()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v0, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6, v5}, Lorg/json/JSONObject;->optString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v0, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v0, p1}, Lcom/nkvt/Login;->postError(Ljava/lang/String;Landroid/widget/Button;)V
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0

    .line 219
    .end local v1    # "hwid":Ljava/lang/String;
    .end local v2    # "initUrl":Ljava/lang/String;
    .end local v3    # "initRes":Lorg/json/JSONObject;
    .end local v4    # "loginUrl":Ljava/lang/String;
    .end local v6    # "loginRes":Lorg/json/JSONObject;
    :goto_0
    goto :goto_1

    .line 217
    :catch_0
    move-exception v0

    .line 218
    .local v0, "e":Ljava/lang/Exception;
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, ""

    invoke-static {}, Lᵔﹶˑʼˊﹳᐧᐧ/ⁱᐧᴵˆʾﹶᴵˎ/ˉᴵʿﾞˋﹳ/ـʻˊˏˎˏˉᵎˊᵢ;->OTkcLʿⁱᵔʽʿיˑٴˆˏʾˉᵎˈᐧˎmuAAD()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/Exception;->getMessage()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {p0, v1, p1}, Lcom/nkvt/Login;->postError(Ljava/lang/String;Landroid/widget/Button;)V

    .line 220
    .end local v0    # "e":Ljava/lang/Exception;
    :goto_1
    return-void
.end method

.method public synthetic lambda$postError$4$Login(Ljava/lang/String;Landroid/widget/Button;)V
    .locals 2
    .param p1, "message"    # Ljava/lang/String;
    .param p2, "loginButton"    # Landroid/widget/Button;

    .line 259
    invoke-direct {p0, p1}, Lcom/nkvt/Login;->showToast(Ljava/lang/String;)V

    .line 260
    const/4 v0, 0x1

    invoke-virtual {p2, v0}, Landroid/widget/Button;->setEnabled(Z)V

    .line 261
    iget-object v0, p0, Lcom/nkvt/Login;->loadingBar:Landroid/widget/ProgressBar;

    const/16 v1, 0x8

    invoke-virtual {v0, v1}, Landroid/widget/ProgressBar;->setVisibility(I)V

    .line 262
    iget-object v0, p0, Lcom/nkvt/Login;->loadingText:Landroid/widget/TextView;

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setVisibility(I)V

    .line 263
    return-void
.end method

.method public synthetic lambda$showToast$5$Login(Ljava/lang/String;)V
    .locals 2
    .param p1, "message"    # Ljava/lang/String;

    .line 268
    iget-object v0, p0, Lcom/nkvt/Login;->context:Landroid/content/Context;

    const/4 v1, 0x0

    invoke-static {v0, p1, v1}, Landroid/widget/Toast;->makeText(Landroid/content/Context;Ljava/lang/CharSequence;I)Landroid/widget/Toast;

    move-result-object v0

    invoke-virtual {v0}, Landroid/widget/Toast;->show()V

    return-void
.end method
