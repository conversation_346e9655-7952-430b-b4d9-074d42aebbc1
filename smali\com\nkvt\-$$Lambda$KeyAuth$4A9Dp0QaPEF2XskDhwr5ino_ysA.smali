.class public final synthetic Lcom/nkvt/-$$Lambda$KeyAuth$4A9Dp0QaPEF2XskDhwr5ino_ysA;
.super Ljava/lang/Object;
.source "lambda"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic f$0:Lcom/nkvt/KeyAuth;

.field public final synthetic f$1:Ljava/lang/String;

.field public final synthetic f$2:Lcom/nkvt/KeyAuth$LicenseCallback;


# direct methods
.method public synthetic constructor <init>(Lcom/nkvt/KeyAuth;Ljava/lang/String;Lcom/nkvt/KeyAuth$LicenseCallback;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/nkvt/-$$Lambda$KeyAuth$4A9Dp0QaPEF2XskDhwr5ino_ysA;->f$0:Lcom/nkvt/KeyAuth;

    iput-object p2, p0, Lcom/nkvt/-$$Lambda$KeyAuth$4A9Dp0QaPEF2XskDhwr5ino_ysA;->f$1:Ljava/lang/String;

    iput-object p3, p0, Lcom/nkvt/-$$Lambda$KeyAuth$4A9Dp0QaPEF2XskDhwr5ino_ysA;->f$2:Lcom/nkvt/KeyAuth$LicenseCallback;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 3

    iget-object v0, p0, Lcom/nkvt/-$$Lambda$KeyAuth$4A9Dp0QaPEF2XskDhwr5ino_ysA;->f$0:Lcom/nkvt/KeyAuth;

    iget-object v1, p0, Lcom/nkvt/-$$Lambda$KeyAuth$4A9Dp0QaPEF2XskDhwr5ino_ysA;->f$1:Ljava/lang/String;

    iget-object v2, p0, Lcom/nkvt/-$$Lambda$KeyAuth$4A9Dp0QaPEF2XskDhwr5ino_ysA;->f$2:Lcom/nkvt/KeyAuth$LicenseCallback;

    invoke-virtual {v0, v1, v2}, Lcom/nkvt/KeyAuth;->lambda$license$3$KeyAuth(Ljava/lang/String;Lcom/nkvt/KeyAuth$LicenseCallback;)V

    return-void
.end method
