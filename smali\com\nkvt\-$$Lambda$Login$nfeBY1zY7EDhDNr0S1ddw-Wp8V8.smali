.class public final synthetic Lcom/nkvt/-$$Lambda$Login$nfeBY1zY7EDhDNr0S1ddw-Wp8V8;
.super Ljava/lang/Object;
.source "lambda"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic f$0:Lcom/nkvt/Login;

.field public final synthetic f$1:Landroid/widget/Button;

.field public final synthetic f$2:Ljava/lang/String;


# direct methods
.method public synthetic constructor <init>(Lcom/nkvt/Login;Landroid/widget/Button;Ljava/lang/String;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/nkvt/-$$Lambda$Login$nfeBY1zY7EDhDNr0S1ddw-Wp8V8;->f$0:Lcom/nkvt/Login;

    iput-object p2, p0, Lcom/nkvt/-$$Lambda$Login$nfeBY1zY7EDhDNr0S1ddw-Wp8V8;->f$1:Landroid/widget/Button;

    iput-object p3, p0, Lcom/nkvt/-$$Lambda$Login$nfeBY1zY7EDhDNr0S1ddw-Wp8V8;->f$2:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 3

    iget-object v0, p0, Lcom/nkvt/-$$Lambda$Login$nfeBY1zY7EDhDNr0S1ddw-Wp8V8;->f$0:Lcom/nkvt/Login;

    iget-object v1, p0, Lcom/nkvt/-$$Lambda$Login$nfeBY1zY7EDhDNr0S1ddw-Wp8V8;->f$1:Landroid/widget/Button;

    iget-object v2, p0, Lcom/nkvt/-$$Lambda$Login$nfeBY1zY7EDhDNr0S1ddw-Wp8V8;->f$2:Ljava/lang/String;

    invoke-virtual {v0, v1, v2}, Lcom/nkvt/Login;->lambda$null$2$Login(Landroid/widget/Button;Ljava/lang/String;)V

    return-void
.end method
