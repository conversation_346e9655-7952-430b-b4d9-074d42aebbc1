.class public final synthetic Lcom/nkvt/-$$Lambda$Login$TKfCucj51li1-uMY6nySdGCXS8w;
.super Ljava/lang/Object;
.source "lambda"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic f$0:Lcom/nkvt/Login;

.field public final synthetic f$1:Ljava/lang/String;

.field public final synthetic f$2:Landroid/widget/Button;


# direct methods
.method public synthetic constructor <init>(Lcom/nkvt/Login;Ljava/lang/String;Landroid/widget/Button;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/nkvt/-$$Lambda$Login$TKfCucj51li1-uMY6nySdGCXS8w;->f$0:Lcom/nkvt/Login;

    iput-object p2, p0, Lcom/nkvt/-$$Lambda$Login$TKfCucj51li1-uMY6nySdGCXS8w;->f$1:Ljava/lang/String;

    iput-object p3, p0, Lcom/nkvt/-$$Lambda$Login$TKfCucj51li1-uMY6nySdGCXS8w;->f$2:Landroid/widget/Button;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 3

    iget-object v0, p0, Lcom/nkvt/-$$Lambda$Login$TKfCucj51li1-uMY6nySdGCXS8w;->f$0:Lcom/nkvt/Login;

    iget-object v1, p0, Lcom/nkvt/-$$Lambda$Login$TKfCucj51li1-uMY6nySdGCXS8w;->f$1:Ljava/lang/String;

    iget-object v2, p0, Lcom/nkvt/-$$Lambda$Login$TKfCucj51li1-uMY6nySdGCXS8w;->f$2:Landroid/widget/Button;

    invoke-virtual {v0, v1, v2}, Lcom/nkvt/Login;->lambda$postError$4$Login(Ljava/lang/String;Landroid/widget/Button;)V

    return-void
.end method
