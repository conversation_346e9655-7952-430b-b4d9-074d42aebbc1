.class Lcom/topjohnwu/superuser/internal/RootServiceManager$RemoteService;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/topjohnwu/superuser/internal/RootServiceManager;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x8
    name = "RemoteService"
.end annotation


# instance fields
.field final binder:Landroid/os/IBinder;

.field final name:Landroid/content/ComponentName;

.field refCount:I


# direct methods
.method constructor <init>(Landroid/content/ComponentName;Landroid/os/IBinder;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x1

    iput v0, p0, Lcom/topjohnwu/superuser/internal/RootServiceManager$RemoteService;->refCount:I

    iput-object p1, p0, Lcom/topjohnwu/superuser/internal/RootServiceManager$RemoteService;->name:Landroid/content/ComponentName;

    iput-object p2, p0, Lcom/topjohnwu/superuser/internal/RootServiceManager$RemoteService;->binder:Landroid/os/IBinder;

    return-void
.end method
